# Studio Sessions Schema Plan

## Overview
Create a comprehensive Sanity schema for studio sessions with different service types, pricing tiers, and booking options based on the provided pricing structure.

## Data Analysis
From the provided information:
- **Photoshoot (location)**: 2-4 hours (GHC 1600), 4-6 hours (GHC 2500), Base rate (GHC 1200)
- **Studio walk-in (studio artist)**: GHC 400
- **Studio walk-in (Cherry)**: GHC 750
- **Home service (within Accra)**: GHC 1200-1500, 2-3 hours (extra hour GHC 100)

## Schema Structure

### 1. Main Schema: `studioSession`
**Purpose**: Main document type for studio session services

**Fields**:
- `title` (string, required) - Service name
- `slug` (slug, required) - URL-friendly identifier
- `serviceType` (string, required) - Type of service (photoshoot, studio-walkin, home-service)
- `description` (text) - Detailed service description
- `shortDescription` (string) - Brief summary for cards/previews
- `pricing` (object) - Pricing structure (see pricing schema below)
- `duration` (object) - Duration options and details
- `location` (object) - Location-specific information
- `features` (array of strings) - What's included in the service
- `requirements` (text) - Client requirements or preparations
- `availability` (object) - Booking availability settings
- `images` (array of images) - Service gallery images
- `isActive` (boolean) - Whether service is currently offered
- `order` (number) - Display order
- `seo` (object) - SEO metadata

### 2. Pricing Object Structure
**Nested within studioSession**:
- `basePrice` (number) - Starting/base price
- `currency` (string) - Currency code (GHC)
- `pricingTiers` (array) - Different pricing options
  - `name` (string) - Tier name (e.g., "2-4 hours", "4-6 hours")
  - `price` (number) - Tier price
  - `duration` (object) - Min/max hours
  - `description` (string) - What's included
- `extraHourRate` (number) - Additional hour pricing (for applicable services)
- `priceRange` (object) - Min/max pricing for range-based services

### 3. Duration Object Structure
**Nested within studioSession**:
- `minHours` (number) - Minimum session duration
- `maxHours` (number) - Maximum session duration
- `defaultHours` (number) - Standard session length
- `isFlexible` (boolean) - Whether duration can be customized
- `extraHourAvailable` (boolean) - Whether extra hours can be added

### 4. Location Object Structure
**Nested within studioSession**:
- `type` (string) - location, studio, home, mobile
- `address` (string) - Specific address if applicable
- `area` (string) - General area (e.g., "within Accra")
- `travelFee` (number) - Additional travel costs if applicable
- `coordinates` (geopoint) - Map coordinates if needed

### 5. Supporting Schema: `serviceCategory`
**Purpose**: Categorize different types of studio services

**Fields**:
- `title` (string, required)
- `slug` (slug, required)
- `description` (text)
- `icon` (image) - Category icon
- `color` (color) - Brand color for category
- `order` (number)

## Predefined Service Types
Based on the pricing data, we'll create these initial services:

1. **Location Photoshoot**
   - Service Type: `photoshoot`
   - Pricing Tiers: 2-4 hours (GHC 1600), 4-6 hours (GHC 2500)
   - Base Price: GHC 1200

2. **Studio Walk-in (Studio Artist)**
   - Service Type: `studio-walkin`
   - Fixed Price: GHC 400
   - Artist: Studio Artist

3. **Studio Walk-in (Cherry)**
   - Service Type: `studio-walkin`
   - Fixed Price: GHC 750
   - Artist: Cherry (Premium)

4. **Home Service (Accra)**
   - Service Type: `home-service`
   - Price Range: GHC 1200-1500
   - Duration: 2-3 hours
   - Extra Hour: GHC 100

## Query Functions Plan (Following existing patterns)

### 1. GROQ Query Fields (Add to `querry.ts`)
```typescript
// Studio Sessions fields
const studioSessionFields = groq`
  _id,
  _createdAt,
  title,
  "slug": slug.current,
  serviceType,
  description,
  shortDescription,
  pricing,
  duration,
  location,
  features,
  requirements,
  availability,
  images[]{
    ...,
    _type == 'image' => {
      ...,
      "dimensions": asset->metadata.dimensions
    }
  },
  isActive,
  order,
  seo
`;

// Service Category fields
const serviceCategoryFields = groq`
  _id,
  _createdAt,
  title,
  "slug": slug.current,
  description,
  icon{
    ...,
    "dimensions": asset->metadata.dimensions
  },
  color,
  order
`;
```

### 2. Query Exports (Add to `querry.ts`)
- `allStudioSessionsQuery` - Get all active studio sessions
- `studioSessionBySlugQuery(slug)` - Get specific session by slug
- `studioSessionsByTypeQuery(serviceType)` - Filter by service type
- `featuredStudioSessionsQuery` - Get highlighted services
- `availableStudioSessionsQuery` - Get currently bookable services
- `studioSessionsByPriceRangeQuery(min, max)` - Filter by price range
- `homeServicesQuery` - Get mobile/home services only
- `studioServicesQuery` - Get studio-based services only
- `allServiceCategoriesQuery` - Get all service categories
- `serviceCategoryBySlugQuery(slug)` - Get specific category

### 3. Action Functions (Add to `index.ts`)
Following existing patterns like `getAllStoreProducts()`:
- `getAllStudioSessions()` - Get all active studio sessions
- `getStudioSessionBySlug(slug)` - Get specific session by slug
- `getStudioSessionsByType(serviceType)` - Filter by service type
- `getFeaturedStudioSessions()` - Get highlighted services
- `getAvailableStudioSessions()` - Get currently bookable services
- `getStudioSessionsByPriceRange(min, max)` - Filter by price range
- `getHomeServices()` - Get mobile/home services only
- `getStudioServices()` - Get studio-based services only
- `getAllServiceCategories()` - Get all service categories
- `getServiceCategoryBySlug(slug)` - Get specific category

### 4. Utility Functions (Add to `index.ts`)
- `calculateSessionPrice(serviceSlug, duration, extras)` - Calculate total price
- `formatPrice(amount, currency)` - Format price display
- `getPriceRange(serviceSlug)` - Get min/max pricing
- `isServiceAvailable(serviceSlug)` - Check if service is bookable
- `getServiceDuration(serviceSlug)` - Get duration options

## File Structure (Updated to match existing project)
```
lib/sanity/
├── schemaTypes/
│   ├── studio-sessions.ts          # Main studio sessions schema
│   ├── service-category.ts         # Service categories schema
│   └── index.ts                    # Export new schemas (update existing)
├── lib/actions/
│   ├── querry.ts                   # Add studio session queries (update existing)
│   └── index.ts                    # Add studio session functions (update existing)
└── structure.ts                    # Add studio sessions to structure (update existing)
```

### Integration Points:
1. **Schema Types**: Add to existing `lib/sanity/schemaTypes/index.ts`
2. **Queries**: Add to existing `lib/sanity/lib/actions/querry.ts`
3. **Functions**: Add to existing `lib/sanity/lib/actions/index.ts`
4. **Structure**: Add to existing `lib/sanity/structure.ts`

## Structure.ts Updates (Add to existing structure)
```typescript
// Add to the structure.ts file after existing items
S.divider(),
S.listItem()
  .title("Studio Sessions")
  .icon(Camera) // or appropriate icon
  .child(
    S.list()
      .title("Studio Sessions")
      .items([
        S.listItem()
          .title("Studio Sessions")
          .icon(Camera)
          .child(S.documentTypeList("studioSession").title("Studio Sessions")),
        S.listItem()
          .title("Service Categories")
          .icon(Tag)
          .child(S.documentTypeList("serviceCategory").title("Service Categories")),
      ]),
  ),
```

## Implementation Steps (Updated)
1. **Review and approve this plan**
2. **Create schema files**:
   - `lib/sanity/schemaTypes/studio-sessions.ts`
   - `lib/sanity/schemaTypes/service-category.ts`
3. **Update existing files**:
   - Add imports and exports to `lib/sanity/schemaTypes/index.ts`
   - Add query fields and exports to `lib/sanity/lib/actions/querry.ts`
   - Add action functions to `lib/sanity/lib/actions/index.ts`
   - Add structure items to `lib/sanity/structure.ts`
4. **Test integration**:
   - Verify schema compilation in Sanity Studio
   - Test queries and functions work correctly
   - Validate Sanity Studio structure displays properly
5. **Create sample data for testing**
6. **Document usage patterns for the team**

## Export Patterns (Following existing conventions)

### schemaTypes/index.ts additions:
```typescript
import studioSessions from "./studio-sessions";
import serviceCategory from "./service-category";

// Add to schemaTypes array
export const schemaTypes = [
  // ... existing schemas
  studioSessions,
  serviceCategory,
];

// Add to individual exports
export {
  // ... existing exports
  studioSessions,
  serviceCategory,
};
```

### lib/actions/querry.ts additions:
- Follow existing pattern with field definitions first
- Then query exports using those fields
- Use consistent naming conventions

### lib/actions/index.ts additions:
- Follow existing async function patterns
- Use consistent error handling
- Import queries from querry.ts
- Export functions for external use

## Questions for Review
1. Should we add booking/appointment functionality to the schema?
2. Do we need artist profiles as separate documents?
3. Should we include client testimonials/reviews in the schema?
4. Do we need seasonal pricing or promotional pricing fields?
5. Should we track booking history/analytics in the schema?
6. What icon should we use for Studio Sessions in the structure?

Please review this updated plan and let me know if you'd like any modifications before I proceed with the implementation.
