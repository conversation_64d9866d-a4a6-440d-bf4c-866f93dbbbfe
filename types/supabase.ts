import { products } from "./types"

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      class_backend: {
        Row: {
          age: string | null
          business_description: string | null
          business_name: string | null
          communication_medium: string[] | null
          created_at: string
          email: string | null
          full_name: string | null
          id: number
          interested_topics: string[] | null
          message: string | null
          phone_number: number | null
          reference: string | null
          social_referal: string | null
          transaction_id: string | null
        }
        Insert: {
          age?: string | null
          business_description?: string | null
          business_name?: string | null
          communication_medium?: string[] | null
          created_at?: string
          email?: string | null
          full_name?: string | null
          id?: number
          interested_topics?: string[] | null
          message?: string | null
          phone_number?: number | null
          reference?: string | null
          social_referal?: string | null
          transaction_id?: string | null
        }
        Update: {
          age?: string | null
          business_description?: string | null
          business_name?: string | null
          communication_medium?: string[] | null
          created_at?: string
          email?: string | null
          full_name?: string | null
          id?: number
          interested_topics?: string[] | null
          message?: string | null
          phone_number?: number | null
          reference?: string | null
          social_referal?: string | null
          transaction_id?: string | null
        }
        Relationships: []
      }
      influencers: {
        Row: {
          anything_else_we_should_know: string | null
          created_at: string
          email_address: string | null
          full_name: string | null
          id: number
          is_approved: boolean | null
          location: string | null
          monthly_engagement: string | null
          phone_number: string | null
          social_media_handles_with_folowers_count: Json | null
          why_are_you_interested: string | null
        }
        Insert: {
          anything_else_we_should_know?: string | null
          created_at?: string
          email_address?: string | null
          full_name?: string | null
          id?: number
          is_approved?: boolean | null
          location?: string | null
          monthly_engagement?: string | null
          phone_number?: string | null
          social_media_handles_with_folowers_count?: Json | null
          why_are_you_interested?: string | null
        }
        Update: {
          anything_else_we_should_know?: string | null
          created_at?: string
          email_address?: string | null
          full_name?: string | null
          id?: number
          is_approved?: boolean | null
          location?: string | null
          monthly_engagement?: string | null
          phone_number?: string | null
          social_media_handles_with_folowers_count?: Json | null
          why_are_you_interested?: string | null
        }
        Relationships: []
      }
      make_up_artists_club: {
        Row: {
          age: string | null
          created_at: string
          email_address: string | null
          full_name: string | null
          id: number
          location: string | null
          monthly_engagement: string | null
          phone_number: string | null
          social_media_handles: Json | null
        }
        Insert: {
          age?: string | null
          created_at?: string
          email_address?: string | null
          full_name?: string | null
          id?: number
          location?: string | null
          monthly_engagement?: string | null
          phone_number?: string | null
          social_media_handles?: Json | null
        }
        Update: {
          age?: string | null
          created_at?: string
          email_address?: string | null
          full_name?: string | null
          id?: number
          location?: string | null
          monthly_engagement?: string | null
          phone_number?: string | null
          social_media_handles?: Json | null
        }
        Relationships: []
      }
      new_class_backend: {
        Row: {
          age: string | null
          created_at: string
          difficult_aspect: string[] | null
          email: string | null
          full_name: string | null
          id: number
          nost_interest: string[] | null
          occupation: string | null
          phone_number: number | null
          social_referal: string | null
          transaction_id: string | null
          whatsapp_number: number | null
        }
        Insert: {
          age?: string | null
          created_at?: string
          difficult_aspect?: string[] | null
          email?: string | null
          full_name?: string | null
          id?: number
          nost_interest?: string[] | null
          occupation?: string | null
          phone_number?: number | null
          social_referal?: string | null
          transaction_id?: string | null
          whatsapp_number?: number | null
        }
        Update: {
          age?: string | null
          created_at?: string
          difficult_aspect?: string[] | null
          email?: string | null
          full_name?: string | null
          id?: number
          nost_interest?: string[] | null
          occupation?: string | null
          phone_number?: number | null
          social_referal?: string | null
          transaction_id?: string | null
          whatsapp_number?: number | null
        }
        Relationships: []
      }
      reviews: {
        Row: {
          created_at: string
          id: number
          is_approved: boolean | null
          review: number | null
          review_message: string | null
          type: string | null
          user_email: string | null
          user_name: string | null
        }
        Insert: {
          created_at?: string
          id?: number
          is_approved?: boolean | null
          review?: number | null
          review_message?: string | null
          type?: string | null
          user_email?: string | null
          user_name?: string | null
        }
        Update: {
          created_at?: string
          id?: number
          is_approved?: boolean | null
          review?: number | null
          review_message?: string | null
          type?: string | null
          user_email?: string | null
          user_name?: string | null
        }
        Relationships: []
      }
      store_backend: {
        Row: {
          city: string | null
          created_at: string
          email: string | null
          first_name: string | null
          fulfilled: boolean | null
          id: number
          last_name: string | null
          phone: number | null
          products: products[] | null
          reference: string | null
          region: string | null
          street_name: string | null
          transaction_id: string | null
        }
        Insert: {
          city?: string | null
          created_at?: string
          email?: string | null
          first_name?: string | null
          fulfilled?: boolean | null
          id?: number
          last_name?: string | null
          phone?: number | null
          products?: products[] | null
          reference?: string | null
          region?: string | null
          street_name?: string | null
          transaction_id?: string | null
        }
        Update: {
          city?: string | null
          created_at?: string
          email?: string | null
          first_name?: string | null
          fulfilled?: boolean | null
          id?: number
          last_name?: string | null
          phone?: number | null
          products?: products[] | null
          reference?: string | null
          region?: string | null
          street_name?: string | null
          transaction_id?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  storage: {
    Tables: {
      buckets: {
        Row: {
          allowed_mime_types: string[] | null
          avif_autodetection: boolean | null
          created_at: string | null
          file_size_limit: number | null
          id: string
          name: string
          owner: string | null
          owner_id: string | null
          public: boolean | null
          updated_at: string | null
        }
        Insert: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id: string
          name: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Update: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id?: string
          name?: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      migrations: {
        Row: {
          executed_at: string | null
          hash: string
          id: number
          name: string
        }
        Insert: {
          executed_at?: string | null
          hash: string
          id: number
          name: string
        }
        Update: {
          executed_at?: string | null
          hash?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      objects: {
        Row: {
          bucket_id: string | null
          created_at: string | null
          id: string
          last_accessed_at: string | null
          metadata: Json | null
          name: string | null
          owner: string | null
          owner_id: string | null
          path_tokens: string[] | null
          updated_at: string | null
          user_metadata: Json | null
          version: string | null
        }
        Insert: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Update: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "objects_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads: {
        Row: {
          bucket_id: string
          created_at: string
          id: string
          in_progress_size: number
          key: string
          owner_id: string | null
          upload_signature: string
          user_metadata: Json | null
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          id: string
          in_progress_size?: number
          key: string
          owner_id?: string | null
          upload_signature: string
          user_metadata?: Json | null
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          id?: string
          in_progress_size?: number
          key?: string
          owner_id?: string | null
          upload_signature?: string
          user_metadata?: Json | null
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads_parts: {
        Row: {
          bucket_id: string
          created_at: string
          etag: string
          id: string
          key: string
          owner_id: string | null
          part_number: number
          size: number
          upload_id: string
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          etag: string
          id?: string
          key: string
          owner_id?: string | null
          part_number: number
          size?: number
          upload_id: string
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          etag?: string
          id?: string
          key?: string
          owner_id?: string | null
          part_number?: number
          size?: number
          upload_id?: string
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_parts_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "s3_multipart_uploads_parts_upload_id_fkey"
            columns: ["upload_id"]
            isOneToOne: false
            referencedRelation: "s3_multipart_uploads"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      can_insert_object: {
        Args: {
          bucketid: string
          name: string
          owner: string
          metadata: Json
        }
        Returns: undefined
      }
      extension: {
        Args: {
          name: string
        }
        Returns: string
      }
      filename: {
        Args: {
          name: string
        }
        Returns: string
      }
      foldername: {
        Args: {
          name: string
        }
        Returns: string[]
      }
      get_size_by_bucket: {
        Args: Record<PropertyKey, never>
        Returns: {
          size: number
          bucket_id: string
        }[]
      }
      list_multipart_uploads_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          next_key_token?: string
          next_upload_token?: string
        }
        Returns: {
          key: string
          id: string
          created_at: string
        }[]
      }
      list_objects_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          start_after?: string
          next_token?: string
        }
        Returns: {
          name: string
          id: string
          metadata: Json
          updated_at: string
        }[]
      }
      operation: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      search: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
