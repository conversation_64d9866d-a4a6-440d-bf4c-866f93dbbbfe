import { registrationFormSchema } from "@/components/forms/class/class-registration";
import { z } from "zod";

export type img = {
  _type: string;
  alt?: string;
  imagesorder?: number;
  _key?: string;
  asset: {
    _ref: string;
    _type: string;
  };
  hotspot?: {
    y: number;
    height: number;
    _type: string;
    width: number;
    x: number;
  };
  dimensions?: {
    _type: string;
    width: number;
    aspectRatio: number;
    height: number;
  };
};
export type imageComponent = {
  slug: string;
  imageName: string;
  orderid: number;
  images: img[];
};
export type galleryType = {
  _id: string;
  orderid: number;
  imageName: string;
  slug: string;
  images: img[];
  secondimages: img[];
};
export type galleryCategoresType = {
  _id: string;
  imageName: string;
  slug: string;
};

export type faq = {
  orderid: number;
  question: string;
  answer: string;
};

export type aboutType = {
  image: {
    dimensions: {
      height: number;
      _type: string;
      width: number;
      aspectRatio: number;
    };
    hotspot: {
      y: number;
      height: number;
      _type: string;
      width: number;
      x: number;
    };
    _type: string;
    alt: string;
    asset: {
      _ref: string;
      _type: string;
    };
    crop: {
      left: number;
      bottom: number;
      _type: string;
      right: number;
      top: number;
    };
  };
  image1: img;
  image2: img;
  image3: img;
  image4: img;
  image5: img;
  slug: string;
  whatsapp: string;
  title: string;
  subtitle: string;
  instagram: string;
  description: [object];
  description1: [object];
  description2: [object];
  description3: [object];
  _id: string;
  orderid: number;
};

export type rateCardType = {
  coverImage: {
    _type: string;
    alt: string;
    asset: [Object];
    dimensions: [Object];
  };
  features: [[Object]];
  ratetype: "package" | "single";
  currency: "gh" | "usd";
  packageName: string;
  slug: string;
  price: number;
  _id: string;
  orderid: number;
};
export type seyNaturelleProductdType = {
  coverImage: img;
  features: [[Object]];
  currency: "gh" | "usd";
  stock: "in-stock" | "out-of-stock";
  packageName: string;
  slug: string;
  price: number;
  _id: string;
  orderid: number;
};

export type termsType = {
  _id: string;
  orderid: number;
  title: string;
  slug: string;
  description: [[object]];
};

export type BookingReferenceProps = {
  reference: number;
  status: string;
  message: string;
};

export type paystactRefType = {
  reference: string;
  trans: string;
  status: string;
  message: string;
  transaction: string;
  trxref: string;
  redirecturl: string;
};

export type bc360type = {
  _id: string;
  orderid: number;
  title: string;
  slug: string;
  subtitle: string;
  imageone: img;
  descriptionone: [object];
  imagetwo: img;
  descriptiontwo: [object];
  imagethree: img;
  descriptionthree: [object];
  packages: {
    packagename: string;
    price: number;
    _key: string;
    _type: string;
  }[];
};
export type seyNaturelleContenttype = {
  _id: string;
  orderid: number;
  title: string;
  slug: string;
  subtitle: string;
  imageone: img;
  descriptionone: [object];
  imagetwo: img;
  descriptiontwo: [object];
};

export type products = seyNaturelleProductdType & { quantity: number };

const paymentFormSchema = z.object({
  first_name: z.string().min(2, { error: "First Name is required." }),
  last_name: z.string().min(2, { error: "Last Name is required." }),
  email: z.email({ error: "Please enter a valid email address." }),
  phone_number: z.string().transform((data) => Number(data)),
  street_name: z.string().min(2, { error: "Street Name is required." }),
  city: z.string().min(2, { error: "City is required." }),
  region: z.enum(
    [
      "AHAFO",
      "ASHANTI",
      "BONO EAST",
      "BRONG AHAFO",
      "CENTRAL",
      "EASTERN",
      "GREATER ACCRA",
      "NORTH EAST",
      "NORTHERN",
      "OTI",
      "UPPER EAST",
      "UPPER WEST",
      "VOLTA",
      "WESTERN",
      "WESTERN NORTH",
    ],
    {
      error: "You need to select a Region.",
    },
  ),
});

export type paymentData = {
  reference: paystactRefType;
  products: products[];
  payee: z.infer<typeof paymentFormSchema>;
  date: string;
  total_amount: number;
};
export type coursePaymentDataType = {
  reference: paystactRefType;
  payee: z.infer<typeof registrationFormSchema>;
  date: string;
};
