"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Share } from "lucide-react";
import { useState } from "react";
import { useTimeout } from "usehooks-ts";

export default function ShareBanner() {
  const [copied, setCopied] = useState(false);

  useTimeout(
    () => {
      setCopied(false);
    },
    copied ? 1500 : null
  );

  function copyToClipboard() {
    navigator.clipboard.writeText(window.location.href);
    setCopied(true);
  }

  // function handleShare() {
  //  navigator.clipboard.writeText("https://www.purpleskincarecosmetic.com/reviews/new-review")}
  // }
  return (
    <button
      onClick={copyToClipboard}
      className='inline-flex h-8 items-center overflow-hidden whitespace-nowrap rounded-full border bg-white text-sm drop-shadow-xs transition-all duration-200 hover:drop-shadow-md'
    >
      <div className='flex w-full flex-row items-center text-neutral-500'>
        <span className='px-4'>
          <Share className='size-4' />
        </span>
        <hr className='h-8 w-[1px] bg-gray-200' />
      </div>
      <div className='inline-flex w-full items-center px-4'>
        {!copied ? <span>Share This Page</span> : <span>Link Copied</span>}
      </div>
      <div className='flex w-full flex-row items-center text-neutral-500'>
        <hr className='h-8 w-[1px] bg-gray-200' />
        <span className='px-4'>
          {!copied ? (
            <Copy className='size-4' />
          ) : (
            <CopyCheck className='size-4' />
          )}
        </span>
      </div>
    </button>
  );
}
