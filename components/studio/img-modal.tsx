"use client";
import Image from "next/image";
import { Di<PERSON>, <PERSON><PERSON><PERSON>ontent, DialogTrigger } from "@/components/ui/dialog";
import { motion } from "framer-motion";
import { urlFor } from "@/lib/sanity/lib/image";

interface CoverImageProps {
  image: any | undefined;
  gradient?: boolean;
  className?: string;
}
export function ImageModal(props: CoverImageProps) {
  const { image: source } = props;
  const { alt, dimensions } = source;
  return (
    <div className="">
      <Dialog>
        <DialogTrigger>
          <div className="w-full overflow-hidden rounded-sm">
            <motion.div
              whileHover={{ scale: 1.03, transition: { bounce: 1 } }}
              className=""
            >
              <div className="pointer-events-none h-full">
                <Image
                  height={dimensions ? dimensions.height : 960}
                  width={dimensions ? dimensions.width : 600}
                  sizes="600px"
                  className="pointer-events-none h-96 object-cover"
                  placeholder="blur"
                  loading="lazy"
                  blurDataURL={urlFor(source)
                    .height(dimensions ? dimensions.height : 960)
                    .width(dimensions ? dimensions.width : 600)
                    .blur(89)
                    .url()}
                  alt={alt ? alt : "img"}
                  src={urlFor(source)
                    .height(dimensions ? dimensions.height : 960)
                    .width(dimensions ? dimensions.width : 600)
                    .url()}
                />
              </div>
            </motion.div>
          </div>
        </DialogTrigger>
        <DialogContent className="flex h-fit w-96 flex-col items-center justify-center border-none bg-inherit p-0 md:justify-end">
          <div className="pointer-events-none bg-white p-1">
            <Image
              height={960}
              width={600}
              sizes="600px"
              className="pointer-events-none w-96"
              placeholder="blur"
              loading="lazy"
              blurDataURL={urlFor(source)
                .height(dimensions ? dimensions.height : 960)
                .width(dimensions ? dimensions.width : 600)
                .blur(89)
                .url()}
              alt={alt ? alt : "img"}
              src={urlFor(source)
                .height(dimensions ? dimensions.height : 960)
                .width(dimensions ? dimensions.width : 600)
                .url()}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
