import { PolySansBlack, aldineBT } from "@/constants/fonts";
import { black_cherry_text } from "@/constants/svg";
import { cn } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";

const homeLinks = [
  { id: 1, path: "/", name: "Home" },
  { id: 2, path: "/store", name: "Store" },
];
const companyLinks = [
  { id: 1, path: "/about", name: "About Us" },
  { id: 3, path: "/gallery", name: "Gallery" },
  {
    id: 4,
    path: "/bridal-bookings-ratecard",
    name: "Bridal Bookings Ratecard",
  },
];
const subsidiariesLinks = [
  { id: 1, path: "/sey-naturelle", name: "<PERSON>y Naturelle" },
];
const supportLinks = [
  { id: 1, path: "/contact", name: "Contact Us" },
  { id: 2, path: "/contact", name: "Bookings" },
  { id: 3, path: "/contact", name: "Support" },
];
const resourcesLinks = [
  { id: 1, path: "/terms-and-conditions", name: "Terms And Conditions" },
  { id: 2, path: "/reviews", name: "Reviews" },
];
export default function Footer() {
  return (
    <footer className="flex w-full flex-col items-center bg-white">
      <div className="flex w-full max-w-7xl flex-col items-center border-t p-4">
        <div className="grid w-full md:mb-20 md:grid-cols-3 md:gap-2">
          <div className="flex flex-col items-center md:items-start">
            <div>
              <Image
                alt="black_cherry_text"
                src={black_cherry_text}
                className="w-36"
              />
            </div>
            {/* <div className='flex items-center space-x-2'>
              <Link href={""}>
                <Instagram className='h-8 w-8' />
              </Link>
            </div> */}
          </div>
          <div className="mb-4 flex flex-col items-center p-2 md:col-span-2 md:items-end">
            <div className="flex w-full flex-col items-center justify-between gap-4 md:flex-row md:items-start">
              <div className="flex flex-col items-center gap-4 text-center md:items-start md:text-start">
                <h3
                  className={cn(aldineBT.className, "pb-2 text-lg font-bold")}
                >
                  Home
                </h3>
                <div className="flex flex-col space-y-2 text-sm font-medium">
                  {homeLinks.map((link) => (
                    <Link
                      href={link.path}
                      key={link.id}
                      className="text-gray-500 transition-all duration-200 hover:text-black"
                    >
                      <span className="">{link.name}</span>
                    </Link>
                  ))}
                </div>
              </div>
              <div className="flex flex-col items-center gap-4 text-center md:items-start md:text-start">
                <h3
                  className={cn(aldineBT.className, "pb-2 text-lg font-bold")}
                >
                  Subsidiaries
                </h3>
                <div className="flex flex-col space-y-2 text-sm font-medium">
                  {subsidiariesLinks.map((link) => (
                    <Link
                      href={link.path}
                      key={link.id}
                      className="text-gray-500 transition-all duration-200 hover:text-black"
                    >
                      <span className="">{link.name}</span>
                    </Link>
                  ))}
                </div>
              </div>
              <div className="flex flex-col items-center gap-4 text-center md:items-start md:text-start">
                <h3
                  className={cn(aldineBT.className, "pb-2 text-lg font-bold")}
                >
                  Company
                </h3>
                <div className="flex flex-col space-y-2 text-sm font-medium">
                  {companyLinks.map((link) => (
                    <Link
                      href={link.path}
                      key={link.id}
                      className="text-gray-500 transition-all duration-200 hover:text-black"
                    >
                      <span className="">{link.name}</span>
                    </Link>
                  ))}
                </div>
              </div>
              <div className="flex flex-col items-center gap-4 text-center md:items-start md:text-start">
                <h3
                  className={cn(aldineBT.className, "pb-2 text-lg font-bold")}
                >
                  Support
                </h3>
                <div className="flex flex-col space-y-2 text-sm font-medium">
                  {supportLinks.map((l) => (
                    <Link
                      href={l.path}
                      key={l.id}
                      className="text-gray-500 transition-all duration-200 hover:text-black"
                    >
                      <span className="">{l.name}</span>
                    </Link>
                  ))}
                </div>
              </div>
              <div className="flex flex-col items-center gap-4 md:items-start">
                <h3
                  className={cn(aldineBT.className, "pb-2 text-lg font-bold")}
                >
                  Resources
                </h3>
                <div className="flex flex-col space-y-2 text-sm font-medium">
                  {resourcesLinks.map((l) => (
                    <Link
                      href={l.path}
                      key={l.id}
                      className="text-gray-500 transition-all duration-200 hover:text-black"
                    >
                      <span className="">{l.name}</span>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex w-full flex-col items-center justify-center border-t p-2">
          <p className="text-sm font-medium text-gray-500">
            <span>
              ©️ {new Date().getFullYear()} Blackcherry. All rights reserved.
            </span>
            <span>
              <a
                href="https://www.iamjulius.com/"
                className={cn(PolySansBlack.className, "tracking-wider")}
              >
                {" "}
                - iamjulius
              </a>
            </span>
          </p>
        </div>
      </div>
    </footer>
  );
}
