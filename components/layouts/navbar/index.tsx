"use client";
import { buttonVariants } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuIndicator,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { PolySansBlack, aldineBT } from "@/constants/fonts";
import { Logo } from "@/constants/images";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion, useScroll, useVelocity } from "framer-motion";
import { ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { MouseEvent, useEffect, useState } from "react";

export default function Navbar() {
  const [open, setOpen] = useState(false); // State to manage the full screen Menu.If false it is closed and if true it is opened
  const [menuBtnState, setMenuBtnState] = useState(false); // State to manage the hamburger Menu.If false it is closed and if true it is opened
  const [openBtn, setBtnOpen] = useState(false);
  const [navColor, setNavColor] = useState(false);

  const slideDistance = 60; // if we are sliding out a nav bar at the top of the screen, this will be it's height
  const threshold = 200; // only slide it back when scrolling back at velocity above this positive (or zero) value

  const [activeLink, setActiveLink] = useState("");

  let timeout: NodeJS.Timeout;

  const { scrollY } = useScroll();
  const scrollVelocity = useVelocity(scrollY);

  const [isScrollingBack, setIsScrollingBack] = useState(false);
  const [isAtTop, setIsAtTop] = useState(true); // true if the page is not scrolled or fully scrolled back
  const [isInView, setIsInView] = useState(true);

  useEffect(
    () =>
      scrollVelocity.onChange((latest) => {
        if (latest > 0) {
          setIsScrollingBack(false);
          return;
        }
        if (latest < -threshold) {
          setIsScrollingBack(true);
          return;
        }
      }),
    [scrollVelocity],
  );

  useEffect(
    () => scrollY.onChange((latest) => setIsAtTop(latest <= 0)),
    [scrollY],
  );

  useEffect(
    () => setIsInView(isScrollingBack || isAtTop),
    [isScrollingBack, isAtTop],
  );

  function handleMouseEnter(
    e: MouseEvent<HTMLLIElement, globalThis.MouseEvent>,
  ) {
    clearTimeout(timeout); // clear any previous timeout
    const id = e.currentTarget.id;
    setActiveLink(id);
  }

  function handleMouseLeave(
    e: MouseEvent<HTMLLIElement, globalThis.MouseEvent>,
  ) {
    const id = e.currentTarget.id;
    timeout = setTimeout(() => {
      setActiveLink("");
    }, 500); // remove active link after 2 seconds
  }

  // navbar scroll changeBackground function
  const changeBackground = () => {
    if (window.scrollY >= 10) {
      setNavColor(true);
    } else {
      setNavColor(false);
    }
  };

  useEffect(() => {
    changeBackground();
    // adding the event when scroll change background
    window.addEventListener("scroll", changeBackground);
  });

  const handleHanburgerManu = () => {
    if (!menuBtnState) {
      //open
      setMenuBtnState(true);
      setOpen(true);
      setBtnOpen(true);
    } else {
      //close
      setMenuBtnState(false);
      setOpen(false);
      setBtnOpen(false);
    }
  };

  const hideMenu = () => {
    if (open) {
      setOpen(false);
      setMenuBtnState(false);
      setBtnOpen(false);
    }
  };
  return (
    <motion.nav
      // animate={{ y: isInView ? 0 : -slideDistance }}
      // transition={{ duration: 0.2, delay: 0.25, ease: "easeInOut" }}
      className={cn(
        navColor
          ? "bg-opacity-40 backdrop-blur-lg backdrop-filter"
          : "bg-transparent",
        "fixed inset-x-0 top-0 z-400 flex w-full flex-col items-center justify-center border-b bg-white",
      )}
    >
      <div className="mx-auto flex w-full max-w-6xl flex-col px-4 py-2">
        <div className="grid grid-cols-5 items-center">
          <div className="col-span-1">
            <Link href={"/"} className="">
              <Image alt="Blackcherry Logo" src={Logo} className="z-750 w-16" />
            </Link>
          </div>
          <div className="col-span-3 flex items-center justify-center space-x-4">
            <NavigationMenu className="hidden md:flex">
              <NavigationMenuList className="mx-4">
                {/* Bridal Packages */}
                <NavigationMenuItem
                  id="bridal-bookings-ratecard"
                  className="relative rounded-full"
                >
                  <NavigationMenuLink
                    href="/bridal-bookings-ratecard"
                    className={navigationMenuTriggerStyle()}
                  >
                    Bridal Packages
                  </NavigationMenuLink>
                </NavigationMenuItem>
                {/* Studio Sessions */}
                <NavigationMenuItem
                  id="studio-sessions"
                  className="relative rounded-full"
                >
                  <NavigationMenuLink
                    href="/studio-sessions"
                    className={navigationMenuTriggerStyle()}
                  >
                    Studio Sessions
                  </NavigationMenuLink>
                </NavigationMenuItem>

                {/* Gallery */}
                <NavigationMenuItem
                  id="gallery"
                  className="relative rounded-full"
                >
                  <NavigationMenuLink
                    href="/gallery"
                    className={navigationMenuTriggerStyle()}
                  >
                    Gallery
                  </NavigationMenuLink>
                </NavigationMenuItem>

                {/* Store */}
                <NavigationMenuItem
                  id="store"
                  className="relative rounded-full"
                >
                  <NavigationMenuLink
                    href="/store"
                    className={navigationMenuTriggerStyle()}
                  >
                    Store
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem
                  id="sey-naturelle"
                  className="relative rounded-full"
                >
                  <NavigationMenuLink
                    href="/sey-naturelle"
                    className={navigationMenuTriggerStyle()}
                  >
                    Sey Naturelle
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuIndicator />
              </NavigationMenuList>
            </NavigationMenu>
          </div>
          <div className="col-span-1 flex items-center justify-end space-x-6">
            <div className="">
              <Link
                href={"/contact"}
                className={cn(
                  buttonVariants({
                    variant: "black",
                    size: "withIconRight",
                  }),
                  "group space-x-2 font-bold",
                )}
              >
                <span> Contact Us</span>
                <div className="flex items-center justify-center pl-2">
                  <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-white" />
                  <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-white" />
                </div>
              </Link>
            </div>
            <div className="z-650 bg-inherit md:hidden">
              <div className="flex w-6 items-center justify-end">
                <div
                  className="group flex h-6 w-6 cursor-pointer flex-col items-center justify-between"
                  onClick={handleHanburgerManu}
                >
                  {/* the btn */}
                  <span
                    className={`h-[2px] w-full transform cursor-pointer rounded-lg bg-black mix-blend-difference transition duration-300 ease-in-out ${openBtn ? "translate-y-2.5 rotate-45 bg-black" : ""} `}
                  />
                  <span
                    className={`h-[2px] w-full transform cursor-pointer rounded-lg bg-black mix-blend-difference transition duration-300 ease-in-out ${openBtn ? "hidden w-0" : "w-full"} `}
                  />
                  <span
                    className={`h-[2px] w-full transform cursor-pointer rounded-lg bg-black mix-blend-difference transition duration-300 ease-in-out ${openBtn ? "-translate-y-3 -rotate-45 bg-black" : ""} `}
                  />
                </div>
              </div>
            </div>
            {/* nav menu */}
            <AnimatePresence mode="sync">
              {open && (
                <motion.div
                  initial={{ x: 100, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  exit={{ x: -10, opacity: 0 }}
                  className="fixed -inset-x-2 inset-y-0 z-420 h-screen w-screen bg-white"
                >
                  <div className="relative flex h-full w-full flex-col items-start justify-start bg-white">
                    {/* <div className="absolute top-0 left-0">
                      <Image
                        alt='Blackcherry Logo'
                        src={black_cherry_text}
                        className='w-16 z-750 opacity-30'
                      />
                    </div> */}
                    <div className="flex h-full flex-col items-start justify-end p-4">
                      <div>
                        <div className="mb-4 flex flex-col items-start space-y-7">
                          {navLinks.map((l) => {
                            return (
                              <Link href={l.path} key={l.id}>
                                <span
                                  className={cn(
                                    aldineBT.className,
                                    "text-5xl font-bold",
                                  )}
                                >
                                  {l.name}
                                </span>
                              </Link>
                            );
                          })}
                        </div>
                        <div className="flex flex-col items-start space-y-6 border-t py-4">
                          {resourcesLinks.map((l) => {
                            return (
                              <Link href={l.path} key={l.id}>
                                <span
                                  className={cn(
                                    aldineBT.className,
                                    "text-2xl font-bold",
                                  )}
                                >
                                  {l.name}
                                </span>
                              </Link>
                            );
                          })}
                        </div>
                      </div>
                      <div className="flex w-full flex-col border-t py-2">
                        <p className="text-sm font-medium text-gray-500">
                          <span>
                            ©️ {new Date().getFullYear()} Blackcherry. All
                            rights reserved.
                          </span>
                          <span>
                            <a
                              href="https://www.iamjulius.com/"
                              className={cn(
                                PolySansBlack.className,
                                "tracking-wider text-gray-200",
                              )}
                            >
                              {" "}
                              - iamjulius
                            </a>
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </motion.nav>
  );
}

const navLinks = [
  { id: 1, path: "/", name: "Home" },
  { id: 2, path: "/about", name: "About Us" },
  { id: 3, path: "/bridal-bookings-ratecard", name: "Bridal Bookings" },
  { id: 4, path: "/studio-sessions", name: "Studio Sessions" },
  { id: 5, path: "/store", name: "Store" },
  { id: 6, path: "/gallery", name: "Gallery" },
  { id: 7, path: "/contact", name: "Contact Us" },
  { id: 8, path: "/reviews", name: "Reviews" },
];

const subsidiariesLinks = [
  { id: 1, path: "/sey-naturelle", name: "Sey Naturelle" },
  { id: 2, path: "/bc-360", name: "BC 360 Aesthetics" },
  { id: 3, path: "/beauty-business-academy", name: "Beauty Business Academy" },
];
const resourcesLinks = [
  { id: 1, path: "/terms-and-conditions", name: "Terms And Conditions" },
];
