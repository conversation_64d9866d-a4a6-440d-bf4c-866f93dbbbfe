"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  ArrowUpDown,
  CalendarPlus,
  ChevronDown,
  ChevronRight,
  MoreHorizontal,
  Plus,
} from "lucide-react";
import { format, parseISO, set } from "date-fns";
import { Button, buttonVariants } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuIndicator,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Switch } from "@/components/ui/switch";
import { Database } from "@/types/supabase";
import { cn, formatGhanaPhone } from "@/lib/utils";
import { currency } from "@/hooks/use-currency";
import { toast } from "sonner";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { supabaseClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";
import Link from "next/link";

const FormSchema = z.object({
  completed: z.boolean().nullable(),
});

type classBackend = Database["public"]["Tables"]["new_class_backend"]["Row"];

export function CrushClassBackendTable({ data }: { data: classBackend[] }) {
  const router = useRouter();
  const supabase = supabaseClient();
  const [completed, setCompleted] = React.useState<boolean | null>(false);

  // function handleCompleted(value: boolean) {
  //   if (value === true) {
  //     setCompleted(false);
  //   } else {
  //     setCompleted(true);
  //   }
  // }

  // async function onSubmit(
  //   value: boolean,
  //   id: string | null,
  //   first_name: string,
  //   last_name: string,
  //   email: string
  // ) {
  //   toast.loading("Loading...");

  //   const { error, status } = await supabase
  //     .from("Bookings")
  //     .update({ completed: value })
  //     .eq("booking_id", id!);

  //   if (!error) {
  //     const bookinData: BookingProps = {
  //       first_name: first_name,
  //       last_name: last_name,
  //       email: email,
  //     };
  //     if (value === true) {
  //       toast.loading("Sending email...");

  //       await fetch("/api/completed", {
  //         method: "POST",
  //         headers: {
  //           "Content-Type": "application/json", // Fix typo: "Aplication/json" to "application/json"
  //         },
  //         body: JSON.stringify(bookinData),
  //       });
  //       toast.success("Session Completed");
  //     } else {
  //       toast.success("Session Not Completed");
  //     }
  //   } else {
  //     toast.error("Booking failed");
  //     toast("Form Response:", {
  //       description: (
  //         <pre className='mt-2 w-[320px] rounded-md bg-slate-950 p-4'>
  //           <code className='text-white'>
  //             {JSON.stringify(error?.message, null, 2)}
  //           </code>
  //         </pre>
  //       ),
  //     });
  //     console.error(error);
  //   }

  //   router.refresh();
  // }

  const columns: ColumnDef<classBackend>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value: any) =>
            table.toggleAllPageRowsSelected(!!value)
          }
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value: any) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "created_at",
      header: "Paid | Created Date",
      cell: ({ row }) => {
        const dateString: string = row.getValue("created_at");
        const date = parseISO(dateString);
        const formatted = format(date, "do MMM yyyy");
        return <div className="capitalize">{formatted}</div>;
      },
    },
    {
      accessorKey: "transaction_id",
      header: "#Transaction ID",
      cell: ({ row }) => {
        return (
          <div className="whitespace-nowrap">
            <p>{row.getValue("transaction_id")}</p>
          </div>
        );
      },
    },
    {
      accessorKey: "full_name",
      header: "Full Name",
      cell: ({ row }) => {
        return (
          <div className="whitespace-nowrap">
            <p>{row.getValue("full_name")}</p>
          </div>
        );
      },
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }) => {
        return (
          <div className="whitespace-nowrap">
            <p>{row.getValue("email")}</p>
          </div>
        );
      },
    },
    {
      accessorKey: "phone_number",
      header: "Phone Number",
      cell: ({ row }) => {
        return (
          <div className="whitespace-nowrap">
            <p>{row.getValue("phone_number")}</p>
          </div>
        );
      },
    },
    {
      accessorKey: "age",
      header: "Age",
      cell: ({ row }) => {
        return (
          <div className="whitespace-nowrap">
            <p>{row.getValue("age")}</p>
          </div>
        );
      },
    },
    {
      accessorKey: "occupation",
      header: "Business Name",
      cell: ({ row }) => {
        return (
          <div className="whitespace-nowrap">
            <p>{row.getValue("occupation")}</p>
          </div>
        );
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const payment = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() =>
                  router.push(`/admin/crush-courses/${payment.transaction_id}`)
                }
              >
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  navigator.clipboard.writeText(payment.transaction_id!)
                }
              >
                Copy Transaction ID
              </DropdownMenuItem>
            </DropdownMenuContent>
            <DropdownMenuIndicator />
          </DropdownMenu>
        );
      },
    },
  ];

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-center py-4">
        <Input
          placeholder="Filter emails.."
          value={(table.getColumn("email")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("email")?.setFilterValue(event.target.value)
          }
          className="max-w-sm rounded-xl"
        />
      </div>
      <div className="rounded-md border">
        <ScrollArea className="w-full whitespace-nowrap">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="text-muted-foreground flex-1 text-sm">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
