"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Drawer, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Database } from "@/types/supabase";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { format, parseISO } from "date-fns";
import { MoreHorizontal } from "lucide-react";
import * as React from "react";
import { toast } from "sonner";

import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { supabaseClient } from "@/lib/supabase/client";
import { StarSolidIcon } from "../ux/icons";

type reviewsBackend = Database["public"]["Tables"]["reviews"]["Row"];

export function ReviewsBackendTable({ data }: { data: reviewsBackend[] }) {
  const router = useRouter();
  const supabase = supabaseClient();
  const [completed, setCompleted] = React.useState<boolean | null>(false);

  console.log(completed);

  function handleCompleted(value: boolean) {
    if (value === true) {
      setCompleted(false);
    } else {
      setCompleted(true);
    }
  }

  const onSubmit = (value: boolean, id: number | null) => {
    return new Promise<reviewsBackend>(async (resolve, reject) => {
      const { error, data } = await supabase
        .from("reviews")
        .update({ is_approved: value })
        .eq("id", id!)
        .select()
        .single();

      if (error || !data) {
        reject(error);
      }
      resolve(data!);
      router.refresh();
    });
  };

  const columns: ColumnDef<reviewsBackend>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value: any) =>
            table.toggleAllPageRowsSelected(!!value)
          }
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value: any) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "created_at",
      header: "Created Date",
      cell: ({ row }) => {
        const dateString: string = row.getValue("created_at");
        const date = parseISO(dateString);
        const formatted = format(date, "do MMM yyyy");
        return <div className="capitalize">{formatted}</div>;
      },
    },
    {
      accessorKey: "user_name",
      header: "Full Name",
      cell: ({ row }) => {
        return (
          <div className="whitespace-nowrap">
            <p>{row.getValue("user_name")}</p>
          </div>
        );
      },
    },
    {
      accessorKey: "type",
      header: "Review Reference",
      cell: ({ row }) => {
        return (
          <div className="whitespace-nowrap">
            <p>{row.getValue("type")}</p>
          </div>
        );
      },
    },
    {
      accessorKey: "review_message",
      header: () => (
        <div className="flex w-full items-center justify-center">
          <p>Message</p>
        </div>
      ),
      cell: ({ row }) => {
        return (
          <div className="flex w-full flex-col items-center text-center">
            <div className="hidden md:block">
              <Dialog>
                <DialogTrigger>
                  <p>View Message</p>
                </DialogTrigger>
                <DialogContent>
                  <div className="flex flex-col items-center p-4 text-center">
                    <p>{row.getValue("review_message")}</p>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
            <div className="md:hidden">
              <Drawer>
                <DrawerTrigger>
                  <p>View Message</p>
                </DrawerTrigger>
                <DrawerContent>
                  <div className="flex flex-col items-center p-4 text-center">
                    <p>{row.getValue("review_message")}</p>
                  </div>
                </DrawerContent>
              </Drawer>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "review",
      header: () => (
        <div className="flex w-full items-center justify-center">
          <p>Ratings</p>
        </div>
      ),
      cell: ({ row }) => {
        return (
          <div className="flex items-center justify-center space-x-1">
            {[...Array(5)].map((_, i) => {
              const ratingValue = i + 1;

              return (
                <StarSolidIcon
                  key={i}
                  className={cn(
                    "size-5",
                    ratingValue <= Number(row.getValue("review"))
                      ? "text-yellow-400"
                      : "text-gray-300",
                  )}
                />
              );
            })}
          </div>
        );
      },
    },
    {
      accessorKey: "is_approved",
      header: "Approved",
      cell: ({ row }) => {
        const payment = row.original;
        const id = payment.id;
        return (
          <div>
            <Switch
              checked={row.getValue("is_approved")}
              onCheckedChange={(value) => {
                handleCompleted(value);
                toast.promise(onSubmit(value, id!), {
                  loading: "Approving...",
                  success: (data) =>
                    `${
                      data.is_approved
                        ? `Approved ${data.user_name}'s Review!`
                        : `${data.user_name} Review is not Approved!`
                    }`,
                  error: (err) => `Error: ${err.message}`,
                });
              }}
            />
          </div>
        );
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const payment = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() =>
                  navigator.clipboard.writeText(payment.user_email!)
                }
              >
                Copy Email
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full">
      <div className="rounded-md border">
        <ScrollArea className="w-full whitespace-nowrap">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="text-muted-foreground flex-1 text-sm">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
