"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  ArrowUpDown,
  CalendarPlus,
  ChevronDown,
  ChevronRight,
  MoreHorizontal,
  Plus,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuIndicator,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { format, parseISO, set } from "date-fns";
import { Button, buttonVariants } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Switch } from "@/components/ui/switch";
import { Database } from "@/types/supabase";
import { calculateTotalPrice, cn, formatGhanaPhone } from "@/lib/utils";
import { currency } from "@/hooks/use-currency";
import { toast } from "sonner";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { supabaseClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { aldineBT } from "@/constants/fonts";

const FormSchema = z.object({
  completed: z.boolean().nullable(),
});

type storeBackend = Database["public"]["Tables"]["store_backend"]["Row"];

export function StoreBackendTable({ data }: { data: storeBackend[] }) {
  const router = useRouter();
  const supabase = supabaseClient();
  const [completed, setCompleted] = React.useState<boolean | null>(false);

  function handleCompleted(value: boolean) {
    if (value === true) {
      setCompleted(false);
    } else {
      setCompleted(true);
    }
  }

  const onSubmit = (value: boolean, id: number | null) => {
    return new Promise(async (resolve, reject) => {
      const supabase = supabaseClient();

      const { error, data } = await supabase
        .from("store_backend")
        .update({
          fulfilled: value,
        })
        .eq("id", id!)
        .select()
        .single();

      if (error) {
        reject(error);
      }
      console.log(data);
      resolve(data);
      if (value === true) {
        toast.info("Order has been marked as fulfilled");
        await fetch("/api/fulfillment-confirmation", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        });
      }
      router.refresh();
    });
  };

  const columns: ColumnDef<storeBackend>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value: any) =>
            table.toggleAllPageRowsSelected(!!value)
          }
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value: any) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "created_at",
      header: "Created Date",
      cell: ({ row }) => {
        const dateString: string = row.getValue("created_at");
        const date = parseISO(dateString);
        const formatted = format(date, "do MMM yyyy");
        return <div className="capitalize">{formatted}</div>;
      },
    },
    {
      accessorKey: "first_name",
      header: "Payee Name",
      cell: ({ row }) => {
        return (
          <div className="whitespace-nowrap">
            <p>{row.getValue("first_name")}</p>
          </div>
        );
      },
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }) => {
        const payment = row.original;
        const totalPrice = calculateTotalPrice(payment?.products!);
        return (
          <div className="whitespace-nowrap">
            <Dialog>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <DialogTrigger asChild>
                      <p>{row.getValue("email")}</p>
                    </DialogTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Click To view Customer Details</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <DialogContent className="w-80">
                <h2 className={cn(aldineBT.className, "text-2xl")}>
                  Customer Details
                </h2>
                <div className="divide-y rounded-xl border">
                  <p className="flex w-full flex-col divide-y">
                    <span className={cn("p-2 font-semibold")}>Full Name</span>
                    <span className="p-2 text-gray-400">
                      {payment.first_name} {payment.last_name}
                    </span>
                  </p>
                  <p className="flex w-full flex-col divide-y">
                    <span className={cn("p-2 font-semibold")}>Email</span>
                    <span className="p-2 text-gray-400">{payment.email}</span>
                  </p>
                  <p className="flex w-full flex-col divide-y">
                    <span className={cn("p-2 font-semibold")}>
                      Phone Number
                    </span>
                    <span className="p-2 text-gray-400">
                      {formatGhanaPhone(payment.phone!)}
                    </span>
                  </p>
                </div>
                {/* delivery address */}
                <div className="divide-y rounded-xl border">
                  <p className="flex w-full flex-col divide-y">
                    <span className={cn("p-2 font-semibold")}>
                      Delivery Address
                    </span>
                    <span className="p-2 text-gray-400 capitalize">
                      {payment.street_name}, {payment.city}, {payment.region}
                    </span>
                  </p>
                </div>
                {/* product details */}
                <div className="divide-y rounded-xl border">
                  <p className="flex w-full flex-col divide-y">
                    <span className={cn("p-2 font-semibold")}>Products</span>
                    <span className="p-2 text-gray-400 capitalize">
                      {payment?.products!.map((d) => {
                        return `${d.packageName},`;
                      })}
                    </span>
                  </p>
                  <p className="flex w-full flex-col divide-y">
                    <span className={cn("p-2 font-semibold")}>Total Price</span>
                    <span className="p-2 text-gray-400 capitalize">NaN</span>
                  </p>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        );
      },
    },
    {
      accessorKey: "fulfilled",
      header: "Fulfilled",
      cell: ({ row }) => {
        const payment = row.original;
        const id = payment.id;
        return (
          <div>
            <Switch
              checked={row.getValue("fulfilled")}
              onCheckedChange={(value) => {
                handleCompleted(value);
                toast.promise(onSubmit(value, id!), {
                  loading: "Fulfilling...",
                  success: (data: any) =>
                    `${
                      data[0].fulfilled
                        ? `Fulfilled  ${data[0].first_name}'s ${data[0].product_name} order!`
                        : `${data[0].product_name} order is not fulfilled!`
                    }`,
                  error: (err) => `Error: ${err.message}`,
                });
              }}
            />
          </div>
        );
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const payment = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() =>
                  navigator.clipboard.writeText(payment.transaction_id!)
                }
              >
                Copy Transaction ID
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  navigator.clipboard.writeText(payment.reference!)
                }
              >
                Copy Reference ID
              </DropdownMenuItem>
            </DropdownMenuContent>
            <DropdownMenuIndicator />
          </DropdownMenu>
        );
      },
    },
  ];

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-center py-4">
        <Input
          placeholder="Filter events.."
          value={(table.getColumn("email")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("email")?.setFilterValue(event.target.value)
          }
          className="max-w-sm rounded-xl"
        />
      </div>
      <div className="rounded-md border">
        <ScrollArea className="w-full whitespace-nowrap">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="text-muted-foreground flex-1 text-sm">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
