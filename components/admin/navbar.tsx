"use client";
import { aldineBT } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import {
  CalendarCheck2,
  ChevronRight,
  DoorOpen,
  Home,
  ShoppingBasket,
} from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { motion, useScroll, useVelocity } from "framer-motion";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Database } from "@/types/supabase";
import { toast } from "sonner";
import { useEffect, useState } from "react";
import { StarSolidIcon } from "../ux/icons";
import { supabaseClient } from "@/lib/supabase/client";
import { User } from "@supabase/supabase-js";

const NavbarLinks = [
  {
    id: 2,
    icon: <ShoppingBasket className="h-4 w-4" />,
    title: "Store",
    href: "/admin/store",
  },
  {
    id: 3,
    icon: <CalendarCheck2 className="h-4 w-4" />,
    title: "Old",
    href: "/admin/courses",
  },
  {
    id: 4,
    icon: <CalendarCheck2 className="h-4 w-4" />,
    title: "Crush",
    href: "/admin/crush-courses",
  },
  {
    id: 1,
    icon: <StarSolidIcon className="h-4 w-4" />,
    title: "Reviews",
    href: "/admin/reviews",
  },
];
export default function AdminNavbar({ user }: { user: User }) {
  const pathname = usePathname();
  const supabase = supabaseClient();
  const router = useRouter();

  const slideDistance = 60; // if we are sliding out a nav bar at the top of the screen, this will be it's height
  const threshold = 200; // only slide it back when scrolling back at velocity above this positive (or zero) value

  const { scrollY } = useScroll();
  const scrollVelocity = useVelocity(scrollY);

  const [isScrollingBack, setIsScrollingBack] = useState(false);
  const [isAtTop, setIsAtTop] = useState(true); // true if the page is not scrolled or fully scrolled back
  const [isInView, setIsInView] = useState(true);
  // const [isUser, setIsUser] = useState(false);

  // useEffect(() => {
  //   async function getSessionUser() {
  //     const supabase = supabaseClient();

  //     const {
  //       data: { user },
  //     } = await supabase.auth.getUser();

  //     if (!user) {
  //       setIsUser(false);
  //     } else {
  //       setIsUser(true);
  //     }
  //   }
  //   getSessionUser();
  // }, []);

  useEffect(
    () =>
      scrollVelocity.onChange((latest) => {
        if (latest > 0) {
          setIsScrollingBack(false);
          return;
        }
        if (latest < -threshold) {
          setIsScrollingBack(true);
          return;
        }
      }),
    [scrollVelocity],
  );

  useEffect(
    () => scrollY.onChange((latest) => setIsAtTop(latest <= 0)),
    [scrollY],
  );

  useEffect(
    () => setIsInView(isScrollingBack || isAtTop),
    [isScrollingBack, isAtTop],
  );

  const SignOut = async () => {
    await supabase.auth.signOut();
    router.refresh();
  };

  async function handleSignOut() {
    toast.promise(SignOut, {
      loading: "Signing out..",
      success: (data) => {
        return "Signed Out";
      },
      error: "Error",
    });
    router.refresh();
  }

  return (
    <motion.div
      animate={{ y: isInView ? 0 : -slideDistance }}
      transition={{ duration: 0.2, delay: 0.25, ease: "easeInOut" }}
      className="flex w-full space-x-1 rounded-full border bg-white p-1"
    >
      {NavbarLinks.map((link) => (
        <div key={link.id} className="relative z-10 w-fit">
          <Link
            href={link.href}
            className={cn(
              "inline-flex h-10 items-center space-x-2 pr-4 pl-2 text-sm font-medium text-gray-800 hover:text-gray-900",
            )}
          >
            <span className="z-20">{link.icon}</span>
            <span className="z-20">{link.title}</span>
          </Link>
          {pathname === link.href && (
            <motion.div
              layoutId="admin-nav-links"
              className="absolute inset-0 z-0 rounded-full bg-gray-200"
            />
          )}
        </div>
      ))}

      <Popover>
        <PopoverTrigger className="">
          <div className="relative inline-flex h-10 w-10 items-center justify-center space-x-2 rounded-full bg-gray-100 text-sm font-medium text-gray-800 hover:text-gray-900">
            <span className="">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="h-4 w-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                />
              </svg>
            </span>
          </div>
        </PopoverTrigger>
        <PopoverContent
          align="end"
          alignOffset={-10}
          className="mt-1 w-[220px] overflow-hidden rounded-3xl bg-gray-100 p-0"
        >
          <div
            className={cn(
              "overflow-hidden rounded-b-2xl border-b bg-white pt-2",
            )}
          >
            {/* user details */}
            <div className="flex flex-col border-b py-2 pr-2 pl-4">
              <p className="text-sm font-medium">User Email</p>
              <p className="text-sm text-gray-400">{user.email}</p>
            </div>
          </div>
          <div className="px-4 pt-2 pb-3 text-sm">
            <button
              onClick={handleSignOut}
              className={cn(
                "flex w-full items-center justify-start rounded-none font-medium text-gray-400 hover:text-red-600",
              )}
            >
              <div className="flex items-center space-x-2">
                <span>
                  <DoorOpen className="h-4 w-4" />
                </span>
                <span>Log Out</span>
              </div>
            </button>
          </div>
        </PopoverContent>
      </Popover>
    </motion.div>
  );
}
