"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

import { cn } from "@/lib/utils";
import { aldineBT } from "@/constants/fonts";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Loader2, Mailbox, Plus } from "lucide-react";
import useUuid from "@/hooks/useUuid";
import { format } from "date-fns";
import { coursePaymentDataType, paystactRefType } from "@/types/types";
import { PaystackConsumer } from "react-paystack";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { Database } from "@/types/supabase";

const PAYSTACK_LIVE_API_KEY = process.env.NEXT_PUBLIC_PAYSTACK_LIVE_API_KEY!;
const PAYSTACK_TEST_API_KEY = process.env.NEXT_PUBLIC_PAYSTACK_TEST_API_KEY!;

const interestedTopics = [
  "Brows",
  "Eyeshadow",
  "Foundation application",
  "Color selection",
];
const ages = ["20-25", "26-30", "30-35", "36-40", "40 and above"];
const socialReferal = ["Facebook", "Instagram", "Watsapp", "Website", "others"];

export const registrationFormSchema = z.object({
  full_name: z.string().min(2, { error: "Full Name is required." }),
  email: z.email({ error: "Please enter a valid email address." }),
  phone_number: z.string().transform((data) => Number(data)),
  whatsapp_number: z.string().transform((data) => Number(data)),
  occupation: z.string().min(2, { error: "Occupation is required." }),
  age: z.enum(["20-25", "26-30", "30-35", "36-40", "40 and above"], {
    error: "You need to select one of the options.",
  }),
  most_interested: z.array(z.string()).refine((values) => values.length > 0, {
    error: "You must select at least one",
  }),
  difficult_aspect: z.array(z.string()).refine((values) => values.length > 0, {
    error: "You must select at least one",
  }),
  social_referal: z
    .enum(["Facebook", "Instagram", "Tiktok"], {
      error: "You need to select one of the options.",
    })
    .optional(),
});

export default function ClassRegistrationForm() {
  const supabase = createClientComponentClient<Database>();
  const form = useForm<z.infer<typeof registrationFormSchema>>({
    resolver: zodResolver(registrationFormSchema),
    defaultValues: {
      full_name: "",
      email: "",
      difficult_aspect: [],
      most_interested: [],
      occupation: "",
    },
  });
  const formData = form.watch();

  const date = new Date();
  const [SubmitBtn, setSubmitBtn] = useState(<span>Make Payment</span>);
  const [complete, setComplete] = useState(false);
  const paymentDate = format(date, "LLL d yyyy");
  const uuid = useUuid();
  const reference = formData.full_name.slice(0, 2) + uuid;

  async function onSubmit({ ref }: { ref: paystactRefType }) {
    return new Promise(async (resolve, reject) => {
      setSubmitBtn(<span>sending...</span>);
      const data: coursePaymentDataType = {
        payee: formData,
        reference: ref,
        date: paymentDate,
      };
      console.log(1);

      const { data: supaData, error } = await supabase
        .from("new_class_backend")
        .insert([
          {
            age: data.payee.age,
            difficult_aspect: data.payee.difficult_aspect,
            email: data.payee.email,
            full_name: data.payee.full_name,
            transaction_id: data.reference.transaction,
            nost_interest: data.payee.most_interested,
            occupation: data.payee.occupation,
            phone_number: data.payee.phone_number,
            social_referal: data.payee.social_referal,
            whatsapp_number: data.payee.whatsapp_number,
          },
        ]);
      console.log(2);
      if (error) {
        console.log(9);
        reject(error);
      } else {
        console.log(3);
        await fetch("/api/course-payment-confirmation", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        });
        console.log(4);
        setSubmitBtn(
          <span className="flex items-center space-x-4">
            Message Sent <Mailbox className="h-4 w-5" />
          </span>,
        );
        resolve(supaData);
      }
    });
  }

  const coursePrice = 650;

  const config = {
    reference: reference,
    email: formData.email,
    firstname: formData.full_name,
    phone: formData.phone_number,
    amount: Number(coursePrice + `00`), //Amount is in the country's lowest currency. E.g Kobo, so 20000 kobo = N200
    // publicKey: PAYSTACK_TEST_API_KEY, //Test Api Key
    publicKey: PAYSTACK_LIVE_API_KEY, //Live Api Key
  };

  function handleSuccess(ref: paystactRefType) {
    // Implementation for whatever you want to do with reference and after success call.
    //if the message is approved and the status is success submit the email and reference to the backend.

    if (ref.status === "success") {
      toast.promise(onSubmit({ ref: ref }), {
        loading: "Registering...",
        success: (data: any) => `Registered`,
        error: (err) => `Error: ${err.message}`,
      });

      // onSubmit({ ref: ref });
      setSubmitBtn(<span>Payment Successful</span>);
    } else {
      console.log("didnt work out :(");
      setSubmitBtn(<span>{`Try Again :(`}</span>);
    }

    console.log(ref);
  }

  // you can call this function anything
  const handleClose = () => {
    // implementation for  whatever you want to do when the Paystack dialog closed.
    console.log("closed");
    setSubmitBtn(<span>{"Payment Canceled :("}</span>);
  };

  const componentProps = {
    ...config,
    text: "Make Payment",
    onSuccess: (ref: any) => handleSuccess(ref),
    onClose: handleClose,
  };

  useEffect(() => {
    if (formData.full_name.length > 0 && formData.email.length > 0) {
      setComplete(true);
    } else {
      setComplete(false);
    }
  }, [formData.email.length, formData.full_name.length]);

  const checkDisablilty = {
    disabled: !complete ? true : false,
  };

  return (
    <div className="flex flex-col">
      <Form {...form}>
        <div className="w-72 space-y-6">
          <FormField
            control={form.control}
            name="full_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name(Name As On Id Card)</FormLabel>
                <FormControl>
                  <Input placeholder="Full Name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="phone_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input placeholder="Phone Number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="whatsapp_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>whatsapp Phone Number</FormLabel>
                <FormControl>
                  <Input placeholder="whatsapp Phone Number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="occupation"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Occupation</FormLabel>
                <FormControl>
                  <Input placeholder="Occupation" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="age"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Age.</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-1"
                  >
                    {ages.map((a, i) => (
                      <FormItem
                        key={i}
                        className="flex items-center space-y-0 space-x-3"
                      >
                        <FormControl>
                          <RadioGroupItem value={a} />
                        </FormControl>
                        <FormLabel className="font-normal">{a}</FormLabel>
                      </FormItem>
                    ))}
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="difficult_aspect"
            render={() => (
              <FormItem>
                <div className="mb-4">
                  <FormLabel className="">
                    What is the most difficult aspect of doing makeup for you?
                  </FormLabel>
                </div>
                {interestedTopics.map((item, i) => (
                  <FormField
                    key={i}
                    control={form.control}
                    name="difficult_aspect"
                    render={({ field }) => {
                      return (
                        <FormItem
                          key={i}
                          className="flex flex-row items-start space-y-0 space-x-3"
                        >
                          <FormControl>
                            <Checkbox
                              checked={field.value?.includes(item)}
                              onCheckedChange={(checked: boolean) => {
                                return checked
                                  ? field.onChange([...field.value, item])
                                  : field.onChange(
                                      field.value?.filter(
                                        (value) => value !== item,
                                      ),
                                    );
                              }}
                            />
                          </FormControl>
                          <FormLabel className="font-normal">{item}</FormLabel>
                        </FormItem>
                      );
                    }}
                  />
                ))}
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="most_interested"
            render={() => (
              <FormItem>
                <div className="mb-4">
                  <FormLabel className="">
                    What are you most interested in learning from the training?
                  </FormLabel>
                </div>
                {interestedTopics.map((item, i) => (
                  <FormField
                    key={i}
                    control={form.control}
                    name="most_interested"
                    render={({ field }) => {
                      return (
                        <FormItem
                          key={i}
                          className="flex flex-row items-start space-y-0 space-x-3"
                        >
                          <FormControl>
                            <Checkbox
                              checked={field.value?.includes(item)}
                              onCheckedChange={(checked: boolean) => {
                                return checked
                                  ? field.onChange([...field.value, item])
                                  : field.onChange(
                                      field.value?.filter(
                                        (value) => value !== item,
                                      ),
                                    );
                              }}
                            />
                          </FormControl>
                          <FormLabel className="font-normal">{item}</FormLabel>
                        </FormItem>
                      );
                    }}
                  />
                ))}
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="social_referal"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Where did you see this event?</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-1"
                  >
                    {socialReferal.map((a, i) => (
                      <FormItem
                        key={i}
                        className="flex items-center space-y-0 space-x-3"
                      >
                        <FormControl>
                          <RadioGroupItem value={a} />
                        </FormControl>
                        <FormLabel className="font-normal">{a}</FormLabel>
                      </FormItem>
                    ))}
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* submit btn */}
          {/* @ts-ignore */}
          <PaystackConsumer currency="GHS" {...componentProps}>
            {({ initializePayment }) => (
              <Button
                variant={"outline"}
                type="button"
                className="w-full"
                {...checkDisablilty}
                onClick={() => {
                  initializePayment(handleSuccess, handleClose);
                }}
              >
                {SubmitBtn}
              </Button>
            )}
          </PaystackConsumer>
        </div>
      </Form>
    </div>
  );
}
