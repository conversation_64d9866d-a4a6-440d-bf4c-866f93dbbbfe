"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";
import { aldineBT } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import Link from "next/link";

const contactType = ["CONTACT", "BOOKING", "SUPPORT"];
export const contactformSchema = z.object({
  first_name: z.string().min(2, {
    error: "first name must be at least 2 characters.",
  }),
  email: z.email({
    error: "email is required.",
  }),
  phone: z
    .string()
    .min(7, {
      error: "number must be at least 7 characters.",
    })
    .max(14, {
      error: "number must be at most 14 characters.",
    })
    .transform((data) => Number(data)),
  messagetype: z.enum(["BOOKING", "SUPPORT", "CONTACT"], {
    error: "You need to select one of the options.",
  }),
  message: z.string().min(10, {
    error: "message must be at least 10 characters.",
  }),
});

export default function ContactForm() {
  const form = useForm<z.infer<typeof contactformSchema>>({
    resolver: zodResolver(contactformSchema),
    defaultValues: {
      first_name: "",
      email: "",
      messagetype: "CONTACT",
      message: "",
    },
  });
  const [submit, setSubmit] = useState("submit");
  const formData = form.watch();

  function handleMessageTypeChange(e: any) {
    const packageName = e.target.value;
    form.setValue("messagetype", packageName);
  }

  async function onSubmit(values: z.infer<typeof contactformSchema>) {
    setSubmit("submitting");
    await fetch("/api/contact-send", {
      method: "POST",
      headers: {
        "Content-Type": "application/json", // Fix typo: "Aplication/json" to "application/json"
      },
      body: JSON.stringify(values),
    });

    setSubmit("Message Sent 🎉");
  }

  const messagePlaceholder = () => {
    switch (formData.messagetype) {
      case "CONTACT":
        return "How can we help?";
      case "BOOKING":
        return "Tell us about the event and the package you would like.";
      case "SUPPORT":
        return "Please include your Reference & Transaction Id if it's a support for a Purchase?";
      default:
        return "How can we help?";
    }
  };
  return (
    <div className="sticky top-20">
      <div className="py-2">
        <h2 className={cn(aldineBT.className, "text-2xl font-bold")}>
          Send Us A Message
        </h2>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="w-80 space-y-4">
          <FormField
            control={form.control}
            name="first_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name</FormLabel>
                <FormControl>
                  <Input placeholder="Joe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Address</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input placeholder="(*************" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="messagetype"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Message Type</FormLabel>
                <FormControl>
                  <div className="flex w-full flex-wrap items-center">
                    {contactType.map((cat, i) => (
                      <label
                        key={i}
                        className={cn(
                          buttonVariants({ variant: "outline" }),
                          formData.messagetype === cat
                            ? "border-black bg-white dark:border-gray-800 dark:bg-gray-700"
                            : "text-gray-500",
                          "mr-2 cursor-pointer text-center text-xs font-semibold",
                        )}
                      >
                        {/* //this input is hidden cos there is no appearance for it */}
                        <input
                          type="radio"
                          name="messageType"
                          value={cat}
                          checked={formData.messagetype === cat}
                          onChange={handleMessageTypeChange}
                          className="absolute -left-[999px] appearance-none"
                        />
                        <span data-text={cat} className="text-xs">
                          {cat}
                        </span>
                      </label>
                    ))}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div>
            <div className="flex w-full items-center space-x-2 text-sm text-gray-500">
              <p>View Our </p>{" "}
              <Link href={"/bridal-bookings-ratecard"}>Packages</Link>{" "}
            </div>
          </div>
          <FormField
            control={form.control}
            name="message"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Message</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={messagePlaceholder()}
                    className="h-40"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" variant={"outline"} className="w-full">
            {submit}
          </Button>
        </form>
      </Form>
    </div>
  );
}
