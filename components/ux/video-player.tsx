"use client";
import dynamic from "next/dynamic";
import { useEffect, useState } from "react";

// Dynamically import ReactPlayer to prevent SSR issues
const ReactPlayer = dynamic(() => import("react-player"), {
  ssr: false,
  loading: () => (
    <div className="flex h-[48rem] w-full items-center justify-center bg-gray-900">
      <div className="text-white">Loading video...</div>
    </div>
  ),
});

let videosrc = "https://www.youtube.com/watch?v=gfU1iZnjRZM";

export default function Video() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <div>
      {isMounted ? (
        <ReactPlayer
          width="100%"
          height="48rem"
          url={videosrc}
          loop
          controls={false}
          playing={true}
          muted={true}
        />
      ) : (
        <div className="flex h-[48rem] w-full items-center justify-center bg-gray-900">
          <div className="text-white">Loading video...</div>
        </div>
      )}

      {/* <source src={videosrc} type='video/mp4' /> */}
    </div>
  );
}
