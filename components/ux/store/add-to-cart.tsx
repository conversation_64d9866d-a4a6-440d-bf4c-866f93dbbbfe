"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useCartStore } from "@/constants/store/cart";
import { seyNaturelleProductdType } from "@/types/types";
import { ShoppingCart } from "lucide-react";

export function AddToCart({ product }: { product: seyNaturelleProductdType }) {
  const { addToCart, cartItems, clearCart, removeFromCart } = useCartStore(
    (state) => state
  );
  if (cartItems.includes(product)) {
    return (
      <Button
        variant={"destructive"}
        onClick={() => removeFromCart(product._id)}
        className='space-x-2'
      >
        <ShoppingCart className='size-4' />
        <span>Added | Remove From Cart</span>
      </Button>
    );
  }
  return (
    <Button
      onClick={() => addToCart(product)}
      className='space-x-2 bg-black text-white hover:bg-black/90 items-center '
    >
      <ShoppingCart className='size-4' />
      <span>Add To Cart</span>
    </Button>
  );
}
