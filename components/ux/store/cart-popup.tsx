"use client";

import { buttonVariants } from "@/components/ui/button";
import { useCartStore } from "@/constants/store/cart";
import { currency } from "@/hooks/use-currency";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";

export function CartPopup() {
  const pathname = usePathname();
  const { addToCart, cartItems, clearCart, removeFromCart } = useCartStore(
    (state) => state,
  );

  function getTotalAmount() {
    return cartItems.reduce((total, item) => {
      return total + item.price;
    }, 0);
  }
  return (
    <section
      className={cn("animate-in fixed inset-x-0 bottom-12", {
        hidden: cartItems.length === 0 || pathname === "/store/cart",
        "animate-out": cartItems.length === 0,
      })}
    >
      <div className="mx-auto flex h-12 w-fit max-w-sm items-center justify-between rounded-full border border-neutral-200 bg-white p-1 drop-shadow-xl">
        <div className="flex items-center justify-center divide-x divide-neutral-200 p-2 font-medium">
          <h2 className="px-2">{cartItems.length}</h2>
          <h2 className="px-2">{currency(getTotalAmount(), "GHS")}</h2>
        </div>
        <div>
          <Link
            href={"/store/cart"}
            className={cn(
              buttonVariants({ variant: "outline" }),
              "bg-black text-white shadow-xs",
            )}
          >
            View Cart
          </Link>
        </div>
      </div>
    </section>
  );
}
