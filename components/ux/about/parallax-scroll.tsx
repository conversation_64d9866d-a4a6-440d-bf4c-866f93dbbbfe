"use client";

import ImageComponent from "@/components/studio/img";
import { buttonVariants } from "@/components/ui/button";
import { testimonials } from "@/constants/db";
import { cn } from "@/lib/utils";
import { img } from "@/types/types";
import { useScroll, motion, useTransform } from "framer-motion";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import { useRef } from "react";

export default function ParallaxScroll({ imgs }: { imgs: img[] }) {
  const testimonialsRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: testimonialsRef,
    offset: ["start end", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], [-400, 1]);
  const y2 = useTransform(scrollYProgress, [0, 1], [1, -400]);
  const y3 = useTransform(scrollYProgress, [0, 1], [-300, 200]);
  const y4 = useTransform(scrollYProgress, [0, 1], [20, -300]);
  return (
    <div className='flex flex-col w-full items-center'>
      <div
        ref={testimonialsRef}
        className='flex flex-col w-full max-w-6xl py-10 p-4'
      >
        {/* image carousel with content */}
        <div className='flex relative flex-col w-full z-0 h-[20rem] overflow-hidden'>
          <div className=' top-[-12.5vh] h-[80vh] grid grid-cols-2 md:grid-cols-4'>
            <div className='absolute top-0 h-10 bg-linear-to-b from-white to-white/30' />
            {/* col-1 */}
            <motion.div
              style={{ y: y }}
              className='flex flex-col px-4 md:text-lg space-y-8 text-center'
            >
              <ImageComponent image={imgs[1]} />
              <ImageComponent image={imgs[0]} />
              <ImageComponent image={imgs[4]} />
            </motion.div>
            {/* col-2 */}
            <motion.div
              style={{ y: y2 }}
              className='flex flex-col px-4 md:text-lg space-y-8 text-center'
            >
              <ImageComponent image={imgs[1]} />
              <ImageComponent image={imgs[2]} />
              <ImageComponent image={imgs[3]} />
            </motion.div>
            {/* col-3 */}
            <motion.div
              style={{ y: y3 }}
              className='flex flex-col px-4 md:text-lg space-y-8 text-center'
            >
              <ImageComponent image={imgs[3]} />
              <ImageComponent image={imgs[4]} />
            </motion.div>
            {/* col-4 */}
            <motion.div
              style={{ y: y4 }}
              className='flex flex-col px-4 md:text-lg space-y-8 text-center'
            >
              <ImageComponent image={imgs[5]} />
              <ImageComponent image={imgs[4]} />
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
