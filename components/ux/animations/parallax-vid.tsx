"use client";

import { useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import { cn } from "@/lib/utils";

interface ParallaxVideoProps {
  className?: string;
  height?: string;
  parallaxIntensity?: number;
}

export function ParallaxVideo({
  className,
  height = "h-[44rem]",
  parallaxIntensity = 0.5,
}: ParallaxVideoProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"],
  });

  // Create parallax effect - video moves slower than scroll
  const y = useTransform(
    scrollYProgress,
    [0, 1],
    [`${parallaxIntensity * 100}%`, `-${parallaxIntensity * 100}%`],
  );

  return (
    <div
      ref={containerRef}
      className={cn("relative overflow-hidden", height, className)}
    >
      <motion.div
        style={{ y }}
        className="absolute inset-0 scale-110" // Scale up to prevent gaps during parallax
      >
        <video
          autoPlay={true}
          muted
          playsInline
          loop
          preload="metadata"
          aria-label="video player"
          className="h-[80dvh] w-full object-cover object-[50%_20%]"
        >
          <source src={"/all-lipstics.mp4"} type="video/mp4" />
          Your browser does not support video tag
        </video>
      </motion.div>
    </div>
  );
}
