"use client";

import { cn } from "@/lib/utils";
import { motion, useScroll, useTransform } from "framer-motion";
import dynamic from "next/dynamic";
import { useEffect, useRef, useState } from "react";

// Dynamically import ReactPlayer to prevent SSR issues
const ReactPlayer = dynamic(() => import("react-player"), {
  ssr: false,
  loading: () => (
    <div className="flex h-full w-full items-center justify-center bg-gray-900">
      <div className="text-white">Loading video...</div>
    </div>
  ),
});

interface ParallaxVideoProps {
  videoUrl: string;
  className?: string;
  height?: string;
  parallaxIntensity?: number;
}

export function ParallaxVideo({
  videoUrl,
  className,
  height = "h-[44rem]",
  parallaxIntensity = 0.5,
}: ParallaxVideoProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"],
  });

  // Create parallax effect - video moves slower than scroll
  const y = useTransform(
    scrollYProgress,
    [1, 0],
    [`${parallaxIntensity * 100}%`, `-${parallaxIntensity * 100}%`],
  );

  return (
    <div
      ref={containerRef}
      className={cn("relative overflow-hidden", height, className)}
    >
      <motion.div
        style={{ y }}
        className="absolute inset-0 scale-110" // Scale up to prevent gaps during parallax
      >
        {isMounted ? (
          <ReactPlayer
            url={videoUrl}
            width="100%"
            height="100%"
            playing={true}
            muted={true}
            loop={true}
            controls={false}
            config={{
              youtube: {
                playerVars: {
                  autoplay: 1,
                  mute: 1,
                  loop: 1,
                  controls: 0,
                  showinfo: 0,
                  rel: 0,
                  modestbranding: 1,
                  iv_load_policy: 3,
                },
              },
            }}
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-gray-900">
            <div className="text-white">Loading video...</div>
          </div>
        )}
      </motion.div>
    </div>
  );
}
