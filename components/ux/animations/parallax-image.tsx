"use client";

import { useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import { cn } from "@/lib/utils";
import aL from "@/public/images/class_1.jpeg";
import Image from "next/image";
import { ProgressiveBlur } from "./progressive-blur";
import { Noise } from "./noise";

interface ParallaxVideoProps {
  className?: string;
  height?: string;
  parallaxIntensity?: number;
}

export function ParallaxImage({
  className,
  height = "h-[44rem]",
  parallaxIntensity = 0.5,
}: ParallaxVideoProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"],
  });

  // Create parallax effect - video moves slower than scroll
  const y = useTransform(
    scrollYProgress,
    [1, 0],
    [`${parallaxIntensity * 100}%`, `-${parallaxIntensity * 100}%`],
  );

  return (
    <div
      ref={containerRef}
      className={cn("relative overflow-hidden", height, className)}
    >
      <Noise />
      <motion.div
        style={{ y }}
        className="absolute inset-0 scale-110" // Scale up to prevent gaps during parallax
      >
        <Image
          alt="home"
          src={aL}
          className="absolute inset-0 z-0 h-[44rem] object-cover object-[50%] md:object-left"
        />
      </motion.div>
    </div>
  );
}
