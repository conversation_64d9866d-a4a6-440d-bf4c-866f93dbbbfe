"use client";

import { aldineBT, fibonSans } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import { CVData } from "@/lib/cv-content";
import { motion } from "framer-motion";
import { Award, BookOpen, Briefcase, Users } from "lucide-react";

interface AboutSectionProps {
  cvData: CVData;
}

export default function AboutSection({ cvData }: AboutSectionProps) {
  const { workExperience, education, awards, professionalSummary } = cvData;

  const stats = [
    {
      icon: Briefcase,
      value: `${workExperience.length}+`,
      label: "Years Experience",
      description: "Professional roles",
    },
    {
      icon: BookOpen,
      value: `${education.length}`,
      label: "Degrees",
      description: "Educational qualifications",
    },
    {
      icon: Award,
      value: `${awards.length}+`,
      label: "Awards",
      description: "Professional recognition",
    },
    {
      icon: Users,
      value: "50+",
      label: "Projects",
      description: "Successfully completed",
    },
  ];

  return (
    <section className="bg-white py-20">
      <div className="mx-auto max-w-7xl px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16 text-center"
        >
          <h2
            className={cn(
              aldineBT.className,
              "mb-6 text-4xl font-bold text-slate-900 lg:text-5xl",
            )}
          >
            About Linda
          </h2>
          <div className="mx-auto mb-8 h-1 w-24 bg-blue-600" />
          <p
            className={cn(
              fibonSans.className,
              "mx-auto max-w-3xl text-xl leading-relaxed text-slate-600",
            )}
          >
            A dedicated professional committed to excellence, innovation, and
            making a meaningful impact through strategic thinking and
            collaborative leadership.
          </p>
        </motion.div>

        <div className="grid items-center gap-16 lg:grid-cols-2">
          {/* Left Column - Story */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <h3
              className={cn(
                aldineBT.className,
                "mb-6 text-3xl font-bold text-slate-900",
              )}
            >
              Professional Journey
            </h3>

            <div className="space-y-4">
              <p
                className={cn(
                  fibonSans.className,
                  "text-lg leading-relaxed text-slate-700",
                )}
              >
                {professionalSummary}
              </p>

              <p
                className={cn(
                  fibonSans.className,
                  "text-lg leading-relaxed text-slate-700",
                )}
              >
                With a strong foundation in medical aesthetics and cosmetic
                science, Linda brings innovative approaches to beauty and
                wellness. Her expertise spans from advanced aesthetic treatments
                like Botox and chemical peels to comprehensive beauty education
                and brand consulting.
              </p>

              <p
                className={cn(
                  fibonSans.className,
                  "text-lg leading-relaxed text-slate-700",
                )}
              >
                As the founder of BlackCherry Gh, Linda has established herself
                as a leading voice in Ghana&apos;s beauty industry. Her
                commitment to excellence and continuous learning ensures she
                stays at the forefront of beauty innovations and industry best
                practices.
              </p>
            </div>

            {/* Core Values */}
            <div className="mt-8">
              <h4
                className={cn(
                  aldineBT.className,
                  "mb-4 text-xl font-semibold text-slate-900",
                )}
              >
                Core Values
              </h4>
              <div className="grid grid-cols-2 gap-4">
                {["Excellence", "Innovation", "Beauty", "Education"].map(
                  (value, index) => (
                    <motion.div
                      key={value}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="rounded-lg bg-slate-50 p-4 text-center"
                    >
                      <span
                        className={cn(
                          fibonSans.className,
                          "font-medium text-slate-800",
                        )}
                      >
                        {value}
                      </span>
                    </motion.div>
                  ),
                )}
              </div>
            </div>
          </motion.div>

          {/* Right Column - Stats */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <h3
              className={cn(
                aldineBT.className,
                "mb-8 text-3xl font-bold text-slate-900",
              )}
            >
              Professional Highlights
            </h3>

            <div className="grid grid-cols-2 gap-6">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="rounded-2xl bg-gradient-to-br from-blue-50 to-slate-50 p-6 text-center transition-shadow hover:shadow-lg"
                >
                  <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-xl bg-blue-600">
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                  <div
                    className={cn(
                      aldineBT.className,
                      "mb-2 text-3xl font-bold text-slate-900",
                    )}
                  >
                    {stat.value}
                  </div>
                  <div
                    className={cn(
                      fibonSans.className,
                      "mb-1 font-semibold text-slate-800",
                    )}
                  >
                    {stat.label}
                  </div>
                  <div
                    className={cn(
                      fibonSans.className,
                      "text-sm text-slate-600",
                    )}
                  >
                    {stat.description}
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Recent Achievement Highlight */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="rounded-2xl bg-gradient-to-r from-blue-600 to-slate-700 p-6 text-white"
            >
              <h4 className={cn(aldineBT.className, "mb-3 text-xl font-bold")}>
                Recent Achievement
              </h4>
              <p
                className={cn(
                  fibonSans.className,
                  "leading-relaxed text-blue-100",
                )}
              >
                {awards[0]?.name || "Best Beauty Entrepreneur of the Year"} -
                Recognized for outstanding contribution to Ghana&apos;s beauty
                industry and excellence in beauty entrepreneurship.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
