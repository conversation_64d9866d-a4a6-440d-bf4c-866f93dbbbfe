"use client";

import { aldineBT, fibonSans } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import { CVData } from "@/lib/cv-content";
import { motion } from "framer-motion";
import { 
  Code, 
  Users, 
  Globe, 
  Award, 
  Briefcase, 
  Target,
  TrendingUp,
  MessageSquare,
  Brain,
  CheckCircle
} from "lucide-react";

interface SkillsSectionProps {
  cvData: CVData;
}

export default function SkillsSection({ cvData }: SkillsSectionProps) {
  const { skills, certifications } = cvData;

  const skillCategories = [
    {
      title: "Technical Skills",
      icon: Code,
      skills: skills.technical,
      color: "from-blue-500 to-blue-600"
    },
    {
      title: "Professional Skills",
      icon: Users,
      skills: skills.professional,
      color: "from-green-500 to-green-600"
    },
    {
      title: "Languages",
      icon: Globe,
      skills: skills.languages.map(lang => `${lang.language} (${lang.proficiency})`),
      color: "from-purple-500 to-purple-600"
    }
  ];

  const iconMap: { [key: string]: any } = {
    "Project Management": Target,
    "Data Analysis": TrendingUp,
    "Leadership": Users,
    "Communication": MessageSquare,
    "Problem Solving": Brain,
    "Strategic Planning": Briefcase
  };

  return (
    <section className="py-20 bg-slate-50">
      <div className="max-w-7xl mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className={cn(aldineBT.className, "text-4xl lg:text-5xl font-bold text-slate-900 mb-6")}>
            Skills & Expertise
          </h2>
          <div className="w-24 h-1 bg-blue-600 mx-auto mb-8" />
          <p className={cn(fibonSans.className, "text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed")}>
            A comprehensive skill set developed through years of experience and continuous learning, 
            enabling effective problem-solving and strategic thinking across diverse challenges.
          </p>
        </motion.div>

        {/* Skills Categories */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {skillCategories.map((category, categoryIndex) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: categoryIndex * 0.2 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow"
            >
              {/* Category Header */}
              <div className="text-center mb-6">
                <div className={cn(
                  "inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-4 bg-gradient-to-r",
                  category.color
                )}>
                  <category.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className={cn(aldineBT.className, "text-2xl font-bold text-slate-900")}>
                  {category.title}
                </h3>
              </div>

              {/* Skills List */}
              <div className="space-y-3">
                {category.skills.map((skill, skillIndex) => {
                  const SkillIcon = iconMap[skill] || CheckCircle;
                  return (
                    <motion.div
                      key={skill}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: (categoryIndex * 0.2) + (skillIndex * 0.1) }}
                      viewport={{ once: true }}
                      className="flex items-center space-x-3 p-3 rounded-lg hover:bg-slate-50 transition-colors"
                    >
                      <SkillIcon className="h-5 w-5 text-blue-600 flex-shrink-0" />
                      <span className={cn(fibonSans.className, "text-slate-700 font-medium")}>
                        {skill}
                      </span>
                    </motion.div>
                  );
                })}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Certifications */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-white rounded-2xl p-8 shadow-lg"
        >
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-amber-500 to-orange-600 rounded-2xl mb-4">
              <Award className="h-8 w-8 text-white" />
            </div>
            <h3 className={cn(aldineBT.className, "text-3xl font-bold text-slate-900 mb-4")}>
              Certifications & Professional Development
            </h3>
            <p className={cn(fibonSans.className, "text-slate-600 max-w-2xl mx-auto")}>
              Committed to continuous learning and staying current with industry best practices 
              through professional certifications and ongoing education.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {certifications.map((cert, index) => (
              <motion.div
                key={cert.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl p-6 border border-slate-200 hover:border-blue-300 transition-colors"
              >
                <div className="flex items-start space-x-3">
                  <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Award className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className={cn(fibonSans.className, "font-bold text-slate-900 mb-1")}>
                      {cert.name}
                    </h4>
                    <p className={cn(fibonSans.className, "text-sm text-slate-600 mb-2")}>
                      {cert.organization}
                    </p>
                    <span className="inline-block bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                      {cert.year}
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
