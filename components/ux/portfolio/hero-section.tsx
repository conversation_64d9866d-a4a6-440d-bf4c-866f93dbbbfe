"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { aldineBT, fibonSans } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import { CVData } from "@/lib/cv-content";
import {
  Download,
  Mail,
  MapPin,
  Phone,
  Linkedin,
  ExternalLink,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";

interface HeroSectionProps {
  cvData: CVData;
}

export default function PortfolioHeroSection({ cvData }: HeroSectionProps) {
  const { personalInfo, professionalSummary } = cvData;

  return (
    <section className="relative flex min-h-screen items-center justify-center overflow-hidden bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgb(15_23_42)_1px,transparent_0)] bg-[length:24px_24px]" />
      </div>

      <div className="relative z-10 mx-auto w-full max-w-7xl px-4 py-20">
        <div className="grid items-center gap-12 lg:grid-cols-2">
          {/* Left Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            {/* Name and Title */}
            <div className="space-y-4">
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className={cn(
                  aldineBT.className,
                  "text-5xl leading-tight font-bold text-slate-900 lg:text-7xl",
                )}
              >
                {personalInfo.name}
              </motion.h1>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="flex items-center space-x-2 text-slate-600"
              >
                <MapPin className="h-5 w-5" />
                <span className={cn(fibonSans.className, "text-lg")}>
                  {personalInfo.location}
                </span>
              </motion.div>
            </div>

            {/* Professional Summary */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className={cn(
                fibonSans.className,
                "max-w-2xl text-xl leading-relaxed text-slate-700",
              )}
            >
              {professionalSummary}
            </motion.p>

            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="flex flex-wrap gap-4 text-slate-600"
            >
              <Link
                href={`mailto:${personalInfo.email}`}
                className="flex items-center space-x-2 transition-colors hover:text-blue-600"
              >
                <Mail className="h-4 w-4" />
                <span className={fibonSans.className}>
                  {personalInfo.email}
                </span>
              </Link>

              <Link
                href={`tel:${personalInfo.phone}`}
                className="flex items-center space-x-2 transition-colors hover:text-blue-600"
              >
                <Phone className="h-4 w-4" />
                <span className={fibonSans.className}>
                  {personalInfo.phone}
                </span>
              </Link>

              <Link
                href={`https://${personalInfo.linkedin}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 transition-colors hover:text-blue-600"
              >
                <Linkedin className="h-4 w-4" />
                <span className={fibonSans.className}>LinkedIn</span>
                <ExternalLink className="h-3 w-3" />
              </Link>
            </motion.div>

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
              className="flex flex-wrap gap-4"
            >
              <Button
                size="lg"
                className="bg-slate-900 px-8 py-3 text-white hover:bg-slate-800"
                asChild
              >
                <Link href="#contact">
                  <Mail className="mr-2 h-4 w-4" />
                  Get In Touch
                </Link>
              </Button>

              <Button
                size="lg"
                variant="outline"
                className="border-slate-300 px-8 py-3 text-slate-700 hover:bg-slate-50"
                asChild
              >
                <Link href="/LINDA MENSAH CV 2.pdf" target="_blank">
                  <Download className="mr-2 h-4 w-4" />
                  Download CV
                </Link>
              </Button>
            </motion.div>
          </motion.div>

          {/* Right Column - Professional Photo */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            <div className="relative mx-auto w-full max-w-md">
              {/* Background Decoration */}
              <div className="absolute inset-0 rotate-6 transform rounded-3xl bg-gradient-to-br from-blue-100 to-slate-100" />
              <div className="absolute inset-0 -rotate-3 transform rounded-3xl bg-gradient-to-br from-slate-100 to-blue-100" />

              {/* Photo Container */}
              <div className="relative rounded-3xl bg-white p-8 shadow-2xl">
                <div className="flex aspect-[3/4] items-center justify-center rounded-2xl bg-gradient-to-br from-slate-200 to-slate-300">
                  {/* Placeholder for professional photo */}
                  <div className="text-center text-slate-500">
                    <div className="mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-slate-400">
                      <span
                        className={cn(
                          aldineBT.className,
                          "text-2xl text-white",
                        )}
                      >
                        {personalInfo.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </span>
                    </div>
                    <p className={fibonSans.className}>Professional Photo</p>
                    <p className={cn(fibonSans.className, "mt-2 text-sm")}>
                      Replace with Linda&apos;s professional headshot
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
