"use client";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
  CarouselMinus,
  CarouselNext,
  CarouselPlus,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { useEffect, useState } from "react";
import { imageComponent } from "@/types/types";
import Autoplay from "embla-carousel-autoplay";
import ImageComponent from "@/components/studio/img";
import Image from "next/image";
import { black_cherry_text } from "@/constants/svg";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import { fibonSans, FONT_PLAYFAIR_DISPLAY } from "@/constants/fonts";
import { ProgressiveBlur } from "../animations/progressive-blur";
import { Noise } from "../animations/noise";

export default function HomeHero({
  carouselImages,
}: {
  carouselImages: imageComponent["images"];
}) {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  return (
    <div className="flex w-full flex-col items-center">
      <div className="relative z-0 flex h-[42rem] w-full flex-col items-center justify-center md:h-[48rem]">
        <div className="relative z-10 overflow-hidden">
          <Carousel
            opts={{
              align: "start",
            }}
            setApi={setApi}
            plugins={[
              Autoplay({
                delay: 6000,
              }),
            ]}
            className="relative w-full"
          >
            <CarouselContent>
              {carouselImages.map((img, i) => {
                return (
                  <CarouselItem key={i} className="relative z-10 pl-0">
                    <div className="absolute inset-0 bg-black/60" />
                    <ImageComponent
                      image={img}
                      className="h-[42rem] w-full object-cover md:h-[48rem]"
                    />
                  </CarouselItem>
                );
              })}
            </CarouselContent>
            {/* hero content */}
            <ProgressiveBlur />
            <div className="absolute inset-x-0 bottom-6 z-30 flex h-full flex-col items-center justify-end space-y-6 px-4 pb-10">
              <div className="flex flex-col items-center space-y-2 text-center">
                <Image
                  alt="black_cherry_text"
                  src={black_cherry_text}
                  className="w-44 md:w-64"
                />
                <h1
                  className={cn(
                    FONT_PLAYFAIR_DISPLAY.className,
                    "max-w-2xl text-2xl font-medium text-white md:text-3xl lg:text-4xl",
                  )}
                >
                  Elevating Natural Beauty for High Melanin Ladies
                </h1>{" "}
                <p className={cn("max-w-xl text-base text-white md:text-lg")}>
                  Welcome to Blackcherry, where Linda Mensah&apos;s vision of
                  natural beauty and wellness comes to life. Experience our
                  chemical-free cosmetics, professional beauty services, and
                  transformative skincare treatments.
                </p>
                <div className="flex flex-wrap items-center justify-center gap-4">
                  <Link
                    href="/about"
                    className={cn(
                      buttonVariants({
                        variant: "black",
                        size: "withIconRight",
                      }),
                      "group space-x-2",
                    )}
                  >
                    <span>Discover Our Story</span>
                    <div className="flex items-center justify-center pl-2">
                      <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-white" />
                      <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-white" />
                    </div>
                  </Link>
                  <Link
                    href="/contact"
                    className={cn(
                      buttonVariants({
                        variant: "outline",
                        size: "withIconRight",
                      }),
                      "group space-x-2 border-white text-white hover:bg-white hover:text-black",
                    )}
                  >
                    <span>Book a Consultation</span>
                    <div className="flex items-center justify-center pl-2">
                      <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                      <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                    </div>
                  </Link>
                </div>
              </div>
              <div className="absolute bottom-2 z-30 flex items-center justify-center space-x-2">
                <CarouselMinus className="border border-neutral-200 bg-white text-black hover:bg-gray-100 hover:text-black" />
                <div className="flex items-center space-x-2">
                  {Array.from({ length: count }).map((_, i) => {
                    return (
                      <div
                        key={i}
                        className={cn(
                          "h-1 rounded-full bg-white transition-all duration-300 ease-linear",
                          current === i + 1 ? "w-4" : "w-1",
                        )}
                      />
                    );
                  })}
                </div>
                <CarouselPlus className="border border-neutral-200 bg-white text-black hover:bg-gray-100 hover:text-black" />
              </div>
            </div>
          </Carousel>
        </div>
      </div>
    </div>
  );
}
