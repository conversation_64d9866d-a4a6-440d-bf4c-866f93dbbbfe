"use client";

import { But<PERSON> } from "@/components/ui/button";
import { AnimatePresence, motion } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";
import dynamic from "next/dynamic";
import { useCallback, useEffect, useState } from "react";

// Dynamically import ReactPlayer to prevent SSR issues
const ReactPlayer = dynamic(() => import("react-player"), {
  ssr: false,
  loading: () => (
    <div className="flex h-64 w-full items-center justify-center rounded-lg bg-gray-900">
      <div className="text-white">Loading video...</div>
    </div>
  ),
});

interface EventData {
  id: number;
  title: string;
  description: string;
  vidUrl: string;
}

interface VideoPlayerProps {
  videoUrl: string;
  isActive: boolean;
  title: string;
  onVideoEnd?: () => void;
}

// Enhanced video player component for Framer Motion layout
function EventVideoPlayer({
  videoUrl,
  isActive,
  title,
  onVideoEnd,
}: VideoPlayerProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [videoAspectRatio, setVideoAspectRatio] = useState<
    "landscape" | "portrait"
  >("landscape");

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleError = useCallback(() => {
    setHasError(true);
  }, []);

  const handleReady = useCallback((player: any) => {
    setHasError(false);

    // Detect video aspect ratio
    const internalPlayer = player.getInternalPlayer();
    if (
      internalPlayer &&
      internalPlayer.videoWidth &&
      internalPlayer.videoHeight
    ) {
      const ratio = internalPlayer.videoWidth / internalPlayer.videoHeight;
      setVideoAspectRatio(ratio > 1 ? "landscape" : "portrait");
    }
  }, []);

  const handleVideoEnd = useCallback(() => {
    if (onVideoEnd) {
      onVideoEnd();
    }
  }, [onVideoEnd]);

  const getVideoContainerClasses = () => {
    const baseClasses = "overflow-hidden rounded-lg bg-black shadow-2xl";

    if (videoAspectRatio === "portrait") {
      // Portrait videos: maintain left-aligned positioning and sizing
      return `${baseClasses} h-full max-h-[80vh] aspect-[9/16] w-auto`;
    } else {
      // Landscape videos: fill maximum available width
      return `${baseClasses} aspect-video w-full`;
    }
  };

  if (!isMounted) {
    return (
      <div className="flex h-96 w-full items-center justify-center rounded-lg bg-gray-900">
        <div className="text-white">Loading video...</div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="flex h-96 w-full items-center justify-center rounded-lg bg-gray-800">
        <div className="text-center text-white">
          <p className="mb-2">Unable to load video</p>
          <p className="text-sm text-gray-400">{title}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={getVideoContainerClasses()}>
      <ReactPlayer
        url={videoUrl}
        width="100%"
        height="100%"
        playing={isActive}
        muted={true}
        loop={false}
        controls={false}
        onError={handleError}
        onReady={handleReady}
        onEnded={handleVideoEnd}
        config={{
          file: {
            attributes: {
              preload: "metadata",
              playsInline: true,
            },
          },
        }}
      />
    </div>
  );
}

export function EventsCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Enhanced events data with better titles and descriptions
  const events: EventData[] = [
    {
      id: 1,
      title: "BlackCherry DIY Makeup Class 2023",
      description:
        "Learn professional makeup techniques in our comprehensive DIY class featuring the latest trends and methods.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-class-2023.MP4",
    },
    // {
    //   id: 2,
    //   title: "Advanced DIY Techniques Workshop",
    //   description:
    //     "Master advanced makeup application techniques with hands-on practice and professional guidance.",
    //   vidUrl:
    //     "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-class-2.mov",
    // },
    {
      id: 3,
      title: "Creative Makeup Artistry Session",
      description:
        "Explore creative makeup artistry with innovative techniques and artistic expression methods.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-Class-3.mov",
    },
    // {
    //   id: 4,
    //   title: "Professional Makeup Masterclass",
    //   description:
    //     "Comprehensive masterclass covering professional makeup application for various occasions and events.",
    //   vidUrl:
    //     "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//Blackcherry-DIY-Class.mov",
    // },
  ];

  // Video-end-based auto-progression
  const handleVideoEnd = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % events.length);
  }, [events.length]);

  // Navigation functions
  const goToPrevious = useCallback(() => {
    setCurrentIndex((prev) => (prev - 1 + events.length) % events.length);
  }, [events.length]);

  const goToNext = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % events.length);
  }, [events.length]);

  const currentEvent = events[currentIndex];

  // Animation variants for Framer Motion
  const videoVariants = {
    enter: {
      opacity: 1,
      filter: "blur(0px)",
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
    exit: {
      opacity: 0,
      filter: "blur(4px)",
      transition: {
        duration: 0.5,
        ease: "easeIn",
      },
    },
  };

  return (
    <main className="mx-auto w-full max-w-7xl">
      <section className="relative min-h-[80vh] overflow-hidden">
        {/* Video Layer (Bottom) */}
        <div className="absolute inset-0 z-0">
          <div className="flex h-full items-center justify-start p-4 lg:p-8">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentEvent.id}
                variants={videoVariants}
                initial="exit"
                animate="enter"
                exit="exit"
                className="flex w-full justify-start"
              >
                <EventVideoPlayer
                  videoUrl={currentEvent.vidUrl}
                  isActive={true}
                  title={currentEvent.title}
                  onVideoEnd={handleVideoEnd}
                />
              </motion.div>
            </AnimatePresence>
          </div>
        </div>

        {/* Full-Width Overlay (Middle Layer) */}

        {/* Content Layer (Top) */}
        <div className="relative z-20 flex h-full min-h-[80vh] flex-col justify-end p-6 lg:p-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentEvent.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="text-white"
            >
              <h2 className="mb-4 text-2xl font-bold text-shadow-lg lg:text-3xl">
                {currentEvent.title}
              </h2>
              <p className="mb-8 text-lg leading-relaxed text-gray-100 lg:text-xl">
                {currentEvent.description}
              </p>
            </motion.div>
          </AnimatePresence>

          {/* Navigation Controls */}
          <div className="mt-6 flex gap-4">
            <Button
              variant="outline"
              size="lg"
              className="border-white/30 bg-white/10 text-white backdrop-blur-sm hover:bg-white/20"
              onClick={goToPrevious}
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-white/30 bg-white/10 text-white backdrop-blur-sm hover:bg-white/20"
              onClick={goToNext}
            >
              Next
              <ChevronRight className="ml-2 h-5 w-5" />
            </Button>
          </div>

          {/* Progress indicator */}
          <div className="mt-6 flex gap-2">
            {events.map((_, index) => (
              <div
                key={index}
                className={`h-1 rounded-full transition-all duration-300 ${
                  index === currentIndex ? "w-8 bg-white" : "w-4 bg-white/30"
                }`}
              />
            ))}
          </div>
        </div>
      </section>
    </main>
  );
}
