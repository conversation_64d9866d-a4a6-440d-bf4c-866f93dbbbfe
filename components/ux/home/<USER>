"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { ChevronLeft, ChevronRight } from "lucide-react";
import dynamic from "next/dynamic";
import { useCallback, useEffect, useRef, useState } from "react";

// Dynamically import ReactPlayer to prevent SSR issues
const ReactPlayer = dynamic(() => import("react-player"), {
  ssr: false,
  loading: () => (
    <div className="flex h-64 w-full items-center justify-center rounded-lg bg-gray-900">
      <div className="text-white">Loading video...</div>
    </div>
  ),
});

interface EventData {
  id: number;
  title: string;
  description: string;
  vidUrl: string;
}

interface VideoPlayerProps {
  videoUrl: string;
  isActive: boolean;
  title: string;
  description: string;
  position: "center" | "side";
}

// Format-agnostic video player component with dynamic layout
function EventVideoPlayer({
  videoUrl,
  isActive,
  title,
  description,
  position,
}: VideoPlayerProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [videoAspectRatio, setVideoAspectRatio] = useState<
    "landscape" | "portrait"
  >("landscape");

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleError = useCallback(() => {
    setHasError(true);
  }, []);

  const handleReady = useCallback((player: any) => {
    setHasError(false);

    // Detect video aspect ratio
    const internalPlayer = player.getInternalPlayer();
    if (
      internalPlayer &&
      internalPlayer.videoWidth &&
      internalPlayer.videoHeight
    ) {
      const ratio = internalPlayer.videoWidth / internalPlayer.videoHeight;
      setVideoAspectRatio(ratio > 1 ? "landscape" : "portrait");
    }
  }, []);

  // Dynamic styling based on position and aspect ratio
  const getContainerClasses = () => {
    const baseClasses =
      "relative transition-all duration-700 ease-in-out transform-gpu";

    if (position === "center") {
      return `${baseClasses} w-full opacity-100 scale-100 filter-none z-10`;
    } else {
      return `${baseClasses} w-full opacity-50 scale-75 blur-[2px] z-0 hover:opacity-70 hover:scale-80`;
    }
  };

  const getVideoContainerClasses = () => {
    const baseClasses = "overflow-hidden rounded-lg bg-black shadow-lg";

    if (position === "center") {
      if (videoAspectRatio === "portrait") {
        return `${baseClasses} aspect-[9/16] max-w-sm mx-auto shadow-2xl`;
      } else {
        return `${baseClasses} aspect-video w-full shadow-2xl`;
      }
    } else {
      return `${baseClasses} aspect-video w-full`;
    }
  };

  if (!isMounted) {
    return (
      <div className={getContainerClasses()}>
        <div className="flex h-64 w-full items-center justify-center rounded-lg bg-gray-900">
          <div className="text-white">Loading video...</div>
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className={getContainerClasses()}>
        <div className="flex h-64 w-full items-center justify-center rounded-lg bg-gray-800">
          <div className="text-center text-white">
            <p className="mb-2">Unable to load video</p>
            <p className="text-sm text-gray-400">{title}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={getContainerClasses()}>
      <div className={getVideoContainerClasses()}>
        <ReactPlayer
          url={videoUrl}
          width="100%"
          height="100%"
          playing={isActive}
          muted={true}
          loop={true}
          controls={false}
          onError={handleError}
          onReady={handleReady}
          config={{
            file: {
              attributes: {
                preload: "metadata",
                playsInline: true,
              },
            },
          }}
        />
      </div>
      {position === "center" && (
        <div className="absolute right-0 bottom-0 left-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          <h3 className="mb-1 text-lg font-semibold text-white">{title}</h3>
          <p className="text-sm text-gray-200">{description}</p>
        </div>
      )}
    </div>
  );
}

export function EventsCarousel() {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const autoplayRef = useRef(
    Autoplay({
      delay: 5000,
      stopOnInteraction: true,
    }),
  );

  // Enhanced events data with better titles and descriptions
  const events: EventData[] = [
    {
      id: 1,
      title: "BlackCherry DIY Makeup Class 2023",
      description:
        "Learn professional makeup techniques in our comprehensive DIY class featuring the latest trends and methods.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-class-2023.MP4",
    },
    {
      id: 2,
      title: "Advanced DIY Techniques Workshop",
      description:
        "Master advanced makeup application techniques with hands-on practice and professional guidance.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-class-2.mov",
    },
    {
      id: 3,
      title: "Creative Makeup Artistry Session",
      description:
        "Explore creative makeup artistry with innovative techniques and artistic expression methods.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-Class-3.mov",
    },
    {
      id: 4,
      title: "Professional Makeup Masterclass",
      description:
        "Comprehensive masterclass covering professional makeup application for various occasions and events.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//Blackcherry-DIY-Class.mov",
    },
  ];

  // Carousel position tracking and video control logic
  useEffect(() => {
    if (!api) {
      return;
    }

    setCurrent(api.selectedScrollSnap());

    const onSelect = () => {
      setCurrent(api.selectedScrollSnap());
    };

    api.on("select", onSelect);
    api.on("reInit", onSelect);

    return () => {
      api.off("select", onSelect);
      api.off("reInit", onSelect);
    };
  }, [api]);

  // Fixed center index calculation - current is already the center item
  const centerIndex = current;

  // Get previous and next indices for the 3-video layout
  const getPrevIndex = () => (centerIndex - 1 + events.length) % events.length;
  const getNextIndex = () => (centerIndex + 1) % events.length;

  // Create the 3-video layout with center focus
  const prevIndex = getPrevIndex();
  const nextIndex = getNextIndex();
  const visibleVideos = [
    { ...events[prevIndex], index: prevIndex, position: "side" as const },
    { ...events[centerIndex], index: centerIndex, position: "center" as const },
    { ...events[nextIndex], index: nextIndex, position: "side" as const },
  ];

  return (
    <main className="mx-auto w-full max-w-7xl">
      <section className="relative">
        {/* Custom 3-video layout */}
        <div className="flex min-h-[300px] items-center justify-center gap-2 px-2 py-6 md:min-h-[400px] md:gap-4 md:px-4 md:py-8">
          {/* Previous Video (Left Side) */}
          <div className="hidden w-1/5 flex-shrink-0 justify-end sm:flex md:w-1/4">
            <EventVideoPlayer
              videoUrl={visibleVideos[0].vidUrl}
              isActive={false}
              title={visibleVideos[0].title}
              description={visibleVideos[0].description}
              position="side"
            />
          </div>

          {/* Center Video (Active) */}
          <div className="flex max-w-full flex-grow justify-center sm:max-w-2xl">
            <EventVideoPlayer
              videoUrl={visibleVideos[1].vidUrl}
              isActive={true}
              title={visibleVideos[1].title}
              description={visibleVideos[1].description}
              position="center"
            />
          </div>

          {/* Next Video (Right Side) */}
          <div className="hidden w-1/5 flex-shrink-0 justify-start sm:flex md:w-1/4">
            <EventVideoPlayer
              videoUrl={visibleVideos[2].vidUrl}
              isActive={false}
              title={visibleVideos[2].title}
              description={visibleVideos[2].description}
              position="side"
            />
          </div>
        </div>

        {/* Hidden Carousel for Navigation Logic */}
        <div className="pointer-events-none absolute inset-0 opacity-0">
          <Carousel
            setApi={setApi}
            opts={{
              align: "center",
              loop: true,
            }}
            plugins={[autoplayRef.current]}
            className="h-full w-full"
          >
            <CarouselContent>
              {events.map((event) => (
                <CarouselItem key={event.id} className="basis-full">
                  <div className="h-full w-full" />
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>

        {/* Navigation Controls */}
        <div className="absolute top-1/2 left-4 -translate-y-1/2">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 rounded-full"
            onClick={() => api?.scrollPrev()}
            disabled={!api}
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">Previous slide</span>
          </Button>
        </div>
        <div className="absolute top-1/2 right-4 -translate-y-1/2">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 rounded-full"
            onClick={() => api?.scrollNext()}
            disabled={!api}
          >
            <ChevronRight className="h-4 w-4" />
            <span className="sr-only">Next slide</span>
          </Button>
        </div>
      </section>
    </main>
  );
}
