"use client";

import { But<PERSON> } from "@/components/ui/button";
import { AnimatePresence, motion } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";
import dynamic from "next/dynamic";
import { useCallback, useEffect, useState } from "react";
import { ProgressiveBlur } from "../animations/progressive-blur";

// Dynamically import ReactPlayer to prevent SSR issues
const ReactPlayer = dynamic(() => import("react-player"), {
  ssr: false,
  loading: () => (
    <div className="flex h-64 w-full items-center justify-center rounded-lg bg-gray-900">
      <div className="text-white">Loading video...</div>
    </div>
  ),
});

interface EventData {
  id: number;
  title: string;
  description: string;
  vidUrl: string;
}

interface VideoPlayerProps {
  videoUrl: string;
  isActive: boolean;
  title: string;
  onVideoEnd?: () => void;
  onProgress?: (progress: number) => void;
}

// Enhanced video player component for Framer Motion layout
function EventVideoPlayer({
  videoUrl,
  isActive,
  title,
  onVideoEnd,
  onProgress,
}: VideoPlayerProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [videoAspectRatio, setVideoAspectRatio] = useState<
    "landscape" | "portrait"
  >("landscape");

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleError = useCallback(() => {
    setHasError(true);
  }, []);

  const handleReady = useCallback((player: any) => {
    setHasError(false);

    // Detect video aspect ratio
    const internalPlayer = player.getInternalPlayer();
    if (
      internalPlayer &&
      internalPlayer.videoWidth &&
      internalPlayer.videoHeight
    ) {
      const ratio = internalPlayer.videoWidth / internalPlayer.videoHeight;
      setVideoAspectRatio(ratio > 1 ? "landscape" : "portrait");
    }
  }, []);

  const handleVideoEnd = useCallback(() => {
    if (onVideoEnd) {
      onVideoEnd();
    }
  }, [onVideoEnd]);

  const handleProgress = useCallback(
    (state: { played: number }) => {
      if (onProgress && isActive) {
        onProgress(state.played);
      }
    },
    [onProgress, isActive],
  );

  const getVideoContainerClasses = () => {
    const baseClasses = "overflow-hidden";

    if (videoAspectRatio === "portrait") {
      // Portrait videos: maintain left-aligned positioning and sizing
      return `${baseClasses} h-full max-h-[80vh] w-full`;
    } else {
      // Landscape videos: fill maximum available width
      return `${baseClasses} aspect-video w-full`;
    }
  };

  if (!isMounted) {
    return (
      <div className="flex h-96 w-full">
        <div className="">Loading video...</div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="flex h-96 w-full items-center justify-center">
        <div className="text-center">
          <p className="mb-2">Unable to load video</p>
          <p className="text-sm text-gray-400">{title}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={getVideoContainerClasses()}>
      <ReactPlayer
        url={videoUrl}
        width="100%"
        height="100%"
        playing={isActive}
        muted={true}
        loop={false}
        controls={false}
        onError={handleError}
        onReady={handleReady}
        onEnded={handleVideoEnd}
        onProgress={handleProgress}
        config={{
          file: {
            attributes: {
              preload: "metadata",
              playsInline: true,
            },
          },
        }}
      />
    </div>
  );
}

export function EventsCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Enhanced events data with better titles and descriptions
  const events: EventData[] = [
    {
      id: 1,
      title: "BlackCherry DIY Makeup Class 2023",
      description:
        "Learn professional makeup techniques in our comprehensive DIY class featuring the latest trends and methods.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-class-2023.MP4",
    },
    {
      id: 2,
      title: "Advanced DIY Techniques Workshop",
      description:
        "Master advanced makeup application techniques with hands-on practice and professional guidance.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-class-2.mov",
    },
    {
      id: 3,
      title: "Creative Makeup Artistry Session",
      description:
        "Explore creative makeup artistry with innovative techniques and artistic expression methods.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-Class-3.mov",
    },
    // {
    //   id: 4,
    //   title: "Professional Makeup Masterclass",
    //   description:
    //     "Comprehensive masterclass covering professional makeup application for various occasions and events.",
    //   vidUrl:
    //     "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//Blackcherry-DIY-Class.mov",
    // },
  ];

  const [videoProgress, setVideoProgress] = useState<number[]>(
    new Array(events.length).fill(0),
  );

  // Video progress handler
  const handleVideoProgress = useCallback(
    (progress: number) => {
      setVideoProgress((prev) => {
        const newProgress = [...prev];
        newProgress[currentIndex] = progress;
        return newProgress;
      });
    },
    [currentIndex],
  );

  // Video-end-based auto-progression
  const handleVideoEnd = useCallback(() => {
    // Mark current video as complete (100%)
    setVideoProgress((prev) => {
      const newProgress = [...prev];
      newProgress[currentIndex] = 1;
      return newProgress;
    });

    // Move to next video
    setCurrentIndex((prev) => (prev + 1) % events.length);
  }, [events.length, currentIndex]);

  // Navigation functions
  const goToPrevious = useCallback(() => {
    setCurrentIndex((prev) => (prev - 1 + events.length) % events.length);
    // Reset progress for the new video
    setVideoProgress((prev) => {
      const newProgress = [...prev];
      newProgress[(currentIndex - 1 + events.length) % events.length] = 0;
      return newProgress;
    });
  }, [events.length, currentIndex]);

  const goToNext = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % events.length);
    // Reset progress for the new video
    setVideoProgress((prev) => {
      const newProgress = [...prev];
      newProgress[(currentIndex + 1) % events.length] = 0;
      return newProgress;
    });
  }, [events.length, currentIndex]);

  const currentEvent = events[currentIndex];

  // Animation variants for Framer Motion
  const videoVariants = {
    enter: {
      opacity: 1,
      filter: "blur(0px)",
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
    exit: {
      opacity: 0,
      filter: "blur(4px)",
      transition: {
        duration: 0.5,
        ease: "easeIn",
      },
    },
  };

  return (
    <main className="bg-amber-50">
      <section className="relative min-h-[80vh] w-full overflow-hidden">
        {/* Video Layer (Bottom) */}
        <div className="absolute inset-0 z-0">
          <div className="flex h-full items-center justify-start">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentEvent.id}
                variants={videoVariants}
                initial="exit"
                animate="enter"
                exit="exit"
                className="flex w-full justify-start"
              >
                <EventVideoPlayer
                  videoUrl={currentEvent.vidUrl}
                  isActive={true}
                  title={currentEvent.title}
                  onVideoEnd={handleVideoEnd}
                  onProgress={handleVideoProgress}
                />
              </motion.div>
            </AnimatePresence>
          </div>
          <ProgressiveBlur
            className="absolute inset-0 bg-black/50"
            direction="right"
            blurLayers={5}
            blurIntensity={1}
          />
          {/* Content Layer (Top) */}
          <div className="absolute right-4 bottom-4 max-w-xs">
            <h2>Training Events</h2>
            <AnimatePresence mode="wait">
              <motion.div
                key={currentEvent.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className="text-white"
              >
                <h3 className="mb-4 text-xl font-bold text-shadow-lg lg:text-3xl">
                  {currentEvent.title}
                </h3>
                <p className="mb-8 leading-relaxed text-gray-100">
                  {currentEvent.description}
                </p>
              </motion.div>
            </AnimatePresence>

            {/* Navigation Controls */}
            <div className="mt-6 flex gap-4">
              <Button
                variant="outline"
                size="lg"
                className="border-white/30 bg-white/10 text-white backdrop-blur-sm hover:bg-white/20"
                onClick={goToPrevious}
              >
                <ChevronLeft className="mr-2 h-5 w-5" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-white/30 bg-white/10 text-white backdrop-blur-sm hover:bg-white/20"
                onClick={goToNext}
              >
                Next
                <ChevronRight className="ml-2 h-5 w-5" />
              </Button>
            </div>

            {/* Video Progress indicator */}
            <div className="mt-6 flex gap-2">
              {events.map((_, index) => (
                <div
                  key={index}
                  className="relative h-1 w-8 overflow-hidden rounded-full bg-white/30"
                >
                  {/* Background bar */}
                  <div className="absolute inset-0 rounded-full bg-white/30" />

                  {/* Progress bar */}
                  <div
                    className={`absolute top-0 left-0 h-full rounded-full transition-all duration-300 ${
                      index === currentIndex ? "bg-white" : "bg-white/30"
                    }`}
                    style={{
                      width: `${
                        index === currentIndex
                          ? (videoProgress[index] || 0) * 100
                          : 0
                      }%`,
                    }}
                  />

                  {/* Active indicator glow */}
                  {index === currentIndex && (
                    <div className="absolute inset-0 animate-pulse rounded-full bg-white/20" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
