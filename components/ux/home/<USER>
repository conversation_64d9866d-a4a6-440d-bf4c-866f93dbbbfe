"use client";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import dynamic from "next/dynamic";
import { useCallback, useEffect, useRef, useState } from "react";

// Dynamically import ReactPlayer to prevent SSR issues
const ReactPlayer = dynamic(() => import("react-player"), {
  ssr: false,
  loading: () => (
    <div className="flex h-64 w-full items-center justify-center rounded-lg bg-gray-900">
      <div className="text-white">Loading video...</div>
    </div>
  ),
});

interface EventData {
  id: number;
  title: string;
  description: string;
  vidUrl: string;
}

interface VideoPlayerProps {
  videoUrl: string;
  isActive: boolean;
  title: string;
  description: string;
}

// Format-agnostic video player component
function EventVideoPlayer({
  videoUrl,
  isActive,
  title,
  description,
}: VideoPlayerProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleError = useCallback(() => {
    setHasError(true);
  }, []);

  const handleReady = useCallback(() => {
    setHasError(false);
  }, []);

  if (!isMounted) {
    return (
      <div className="flex h-64 w-full items-center justify-center rounded-lg bg-gray-900">
        <div className="text-white">Loading video...</div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="flex h-64 w-full items-center justify-center rounded-lg bg-gray-800">
        <div className="text-center text-white">
          <p className="mb-2">Unable to load video</p>
          <p className="text-sm text-gray-400">{title}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full">
      <div className="aspect-video w-full overflow-hidden rounded-lg bg-black">
        <ReactPlayer
          url={videoUrl}
          width="100%"
          height="100%"
          playing={isActive}
          muted={true}
          loop={true}
          controls={false}
          onError={handleError}
          onReady={handleReady}
          config={{
            file: {
              attributes: {
                preload: "metadata",
                playsInline: true,
              },
            },
          }}
        />
      </div>
      <div className="absolute right-0 bottom-0 left-0 bg-gradient-to-t from-black/80 to-transparent p-4">
        <h3 className="mb-1 text-lg font-semibold text-white">{title}</h3>
        <p className="text-sm text-gray-200">{description}</p>
      </div>
    </div>
  );
}

export function EventsCarousel() {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const autoplayRef = useRef(
    Autoplay({
      delay: 4000,
      stopOnInteraction: true,
    }),
  );

  // Enhanced events data with better titles and descriptions
  const events: EventData[] = [
    {
      id: 1,
      title: "BlackCherry DIY Makeup Class 2023",
      description:
        "Learn professional makeup techniques in our comprehensive DIY class featuring the latest trends and methods.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-class-2023.MP4",
    },
    {
      id: 2,
      title: "Advanced DIY Techniques Workshop",
      description:
        "Master advanced makeup application techniques with hands-on practice and professional guidance.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-class-2.mov",
    },
    {
      id: 3,
      title: "Creative Makeup Artistry Session",
      description:
        "Explore creative makeup artistry with innovative techniques and artistic expression methods.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-Class-3.mov",
    },
    {
      id: 4,
      title: "Professional Makeup Masterclass",
      description:
        "Comprehensive masterclass covering professional makeup application for various occasions and events.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//Blackcherry-DIY-Class.mov",
    },
  ];

  // Carousel position tracking and video control logic
  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap());

    const onSelect = () => {
      setCurrent(api.selectedScrollSnap());
    };

    api.on("select", onSelect);
    api.on("reInit", onSelect);

    return () => {
      api.off("select", onSelect);
      api.off("reInit", onSelect);
    };
  }, [api]);

  // Calculate center position for 3-item carousel (lg:basis-1/3)
  const getCenterIndex = useCallback(() => {
    if (count === 0) return 0;

    // For a 3-item carousel, the center position logic
    // depends on the current scroll position and total items
    const centerOffset = Math.floor(3 / 2); // Center of 3 visible items
    return (current + centerOffset) % count;
  }, [current, count]);

  const centerIndex = getCenterIndex();

  return (
    <main className="mx-auto w-full max-w-7xl">
      <section className="">
        <Carousel
          setApi={setApi}
          opts={{
            align: "start",
            loop: true,
          }}
          plugins={[autoplayRef.current]}
          className="w-full"
        >
          <CarouselContent>
            {events.map((event, index) => (
              <CarouselItem
                key={event.id}
                className="md:basis-1/2 lg:basis-1/3"
              >
                <div className="p-2">
                  <EventVideoPlayer
                    videoUrl={event.vidUrl}
                    isActive={index === centerIndex}
                    title={event.title}
                    description={event.description}
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      </section>
    </main>
  );
}
