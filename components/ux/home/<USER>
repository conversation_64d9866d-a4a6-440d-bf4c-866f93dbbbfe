"use client";

import { Button } from "@/components/ui/button";
import { AnimatePresence, motion } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";
import dynamic from "next/dynamic";
import { useCallback, useEffect, useRef, useState } from "react";

// Dynamically import ReactPlayer to prevent SSR issues
const ReactPlayer = dynamic(() => import("react-player"), {
  ssr: false,
  loading: () => (
    <div className="flex h-64 w-full items-center justify-center rounded-lg bg-gray-900">
      <div className="text-white">Loading video...</div>
    </div>
  ),
});

interface EventData {
  id: number;
  title: string;
  description: string;
  vidUrl: string;
}

interface VideoPlayerProps {
  videoUrl: string;
  isActive: boolean;
  title: string;
}

// Enhanced video player component for Framer Motion layout
function EventVideoPlayer({ videoUrl, isActive, title }: VideoPlayerProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [videoAspectRatio, setVideoAspectRatio] = useState<
    "landscape" | "portrait"
  >("landscape");

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleError = useCallback(() => {
    setHasError(true);
  }, []);

  const handleReady = useCallback((player: any) => {
    setHasError(false);

    // Detect video aspect ratio
    const internalPlayer = player.getInternalPlayer();
    if (
      internalPlayer &&
      internalPlayer.videoWidth &&
      internalPlayer.videoHeight
    ) {
      const ratio = internalPlayer.videoWidth / internalPlayer.videoHeight;
      setVideoAspectRatio(ratio > 1 ? "landscape" : "portrait");
    }
  }, []);

  const getVideoContainerClasses = () => {
    const baseClasses = "w-full overflow-hidden rounded-lg bg-black shadow-2xl";

    if (videoAspectRatio === "portrait") {
      return `${baseClasses} h-full max-h-[80vh] aspect-[9/16] ml-auto`;
    } else {
      return `${baseClasses} aspect-video ml-auto`;
    }
  };

  if (!isMounted) {
    return (
      <div className="ml-auto flex h-96 w-full items-center justify-center rounded-lg bg-gray-900">
        <div className="text-white">Loading video...</div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="ml-auto flex h-96 w-full items-center justify-center rounded-lg bg-gray-800">
        <div className="text-center text-white">
          <p className="mb-2">Unable to load video</p>
          <p className="text-sm text-gray-400">{title}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={getVideoContainerClasses()}>
      <ReactPlayer
        url={videoUrl}
        width="100%"
        height="100%"
        playing={isActive}
        muted={true}
        loop={true}
        controls={false}
        onError={handleError}
        onReady={handleReady}
        config={{
          file: {
            attributes: {
              preload: "metadata",
              playsInline: true,
            },
          },
        }}
      />
    </div>
  );
}

export function EventsCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Enhanced events data with better titles and descriptions
  const events: EventData[] = [
    {
      id: 1,
      title: "BlackCherry DIY Makeup Class 2023",
      description:
        "Learn professional makeup techniques in our comprehensive DIY class featuring the latest trends and methods.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-class-2023.MP4",
    },
    {
      id: 2,
      title: "Advanced DIY Techniques Workshop",
      description:
        "Master advanced makeup application techniques with hands-on practice and professional guidance.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-class-2.mov",
    },
    {
      id: 3,
      title: "Creative Makeup Artistry Session",
      description:
        "Explore creative makeup artistry with innovative techniques and artistic expression methods.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-Class-3.mov",
    },
    {
      id: 4,
      title: "Professional Makeup Masterclass",
      description:
        "Comprehensive masterclass covering professional makeup application for various occasions and events.",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//Blackcherry-DIY-Class.mov",
    },
  ];

  // Auto-progression logic with 5-second interval
  useEffect(() => {
    const startInterval = () => {
      intervalRef.current = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % events.length);
      }, 5000);
    };

    startInterval();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [events.length]);

  // Navigation functions
  const goToPrevious = useCallback(() => {
    setCurrentIndex((prev) => (prev - 1 + events.length) % events.length);

    // Reset interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    intervalRef.current = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % events.length);
    }, 5000);
  }, [events.length]);

  const goToNext = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % events.length);

    // Reset interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    intervalRef.current = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % events.length);
    }, 5000);
  }, [events.length]);

  const currentEvent = events[currentIndex];

  // Animation variants for Framer Motion
  const videoVariants = {
    enter: {
      opacity: 1,
      filter: "blur(0px)",
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
    exit: {
      opacity: 0,
      filter: "blur(4px)",
      transition: {
        duration: 0.5,
        ease: "easeIn",
      },
    },
  };

  return (
    <main className="mx-auto w-full max-w-7xl">
      <section className="relative">
        {/* CSS Grid Layout - 2 columns on desktop, 1 column on mobile */}
        <div className="grid min-h-[80vh] grid-cols-1 gap-0 lg:grid-cols-2">
          {/* Video Column - Left on desktop, top on mobile */}
          <div className="relative flex items-center justify-end p-4 lg:p-8">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentEvent.id}
                variants={videoVariants}
                initial="exit"
                animate="enter"
                exit="exit"
                className="w-full max-w-lg lg:max-w-none"
              >
                <EventVideoPlayer
                  videoUrl={currentEvent.vidUrl}
                  isActive={true}
                  title={currentEvent.title}
                />
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Content Panel - Right on desktop, bottom on mobile */}
          <div className="bg-opacity-90 relative bg-amber-900 backdrop-blur-sm">
            {/* Progressive blur gradient overlay */}
            <div
              className="absolute inset-0 bg-gradient-to-b from-black/40 via-black/20 to-transparent backdrop-blur-[2px]"
              style={{
                backdropFilter: "blur(8px) saturate(1.2)",
                background:
                  "linear-gradient(to bottom, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%)",
              }}
            />

            {/* Content positioned at bottom */}
            <div className="relative z-10 flex h-full flex-col justify-end p-6 lg:p-8">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentEvent.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  className="text-white"
                >
                  <h2 className="mb-4 text-2xl font-bold text-shadow-lg lg:text-3xl">
                    {currentEvent.title}
                  </h2>
                  <p className="mb-8 text-lg leading-relaxed text-gray-100 lg:text-xl">
                    {currentEvent.description}
                  </p>
                </motion.div>
              </AnimatePresence>

              {/* Navigation Controls */}
              <div className="mt-6 flex gap-4">
                <Button
                  variant="outline"
                  size="lg"
                  className="border-white/30 bg-white/10 text-white backdrop-blur-sm hover:bg-white/20"
                  onClick={goToPrevious}
                >
                  <ChevronLeft className="mr-2 h-5 w-5" />
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-white/30 bg-white/10 text-white backdrop-blur-sm hover:bg-white/20"
                  onClick={goToNext}
                >
                  Next
                  <ChevronRight className="ml-2 h-5 w-5" />
                </Button>
              </div>

              {/* Progress indicator */}
              <div className="mt-6 flex gap-2">
                {events.map((_, index) => (
                  <div
                    key={index}
                    className={`h-1 rounded-full transition-all duration-300 ${
                      index === currentIndex
                        ? "w-8 bg-white"
                        : "w-4 bg-white/30"
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
