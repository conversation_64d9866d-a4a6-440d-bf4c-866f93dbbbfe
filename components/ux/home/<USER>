"use client";

import {
  Carousel,
  CarouselContent,
  Carousel<PERSON>tem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";

export function EventsCarousel() {
  const events = [
    {
      id: 1,
      title: "Event 1",
      description: "Description 1",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-class-2023.MP4",
    },
    {
      id: 2,
      title: "Event 2",
      description: "Description 2",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-class-2.mov",
    },
    {
      id: 3,
      title: "Event 3",
      description: "Description 3",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-Class-3.mov",
    },
    {
      id: 4,
      title: "Event 4",
      description: "Description 4",
      vidUrl:
        "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//Blackcherry-DIY-Class.mov",
    },
  ];
  return (
    <main className="mx-auto w-full max-w-7xl">
      <section className="">
        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          plugins={[
            Autoplay({
              delay: 2000,
            }),
          ]}
          className="w-full"
        >
          <CarouselContent>
            {events.map((e) => (
              <CarouselItem key={e.id} className="md:basis-1/2 lg:basis-1/3">
                <div className="p-1">{e.title}</div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      </section>
    </main>
  );
}
