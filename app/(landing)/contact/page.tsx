import ContactForm from "@/components/forms/contact/contact-form";
import { aldineBT } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import Link from "next/link";

export default function Page() {
  return (
    <div className="flex w-full flex-col items-center">
      <div className="mb-20 flex max-w-4xl flex-col items-center p-4">
        <div className="mb-20 grid gap-8 md:grid-cols-3">
          <div className="flex w-full flex-col items-center space-y-8 md:col-span-2 md:items-start">
            {/* phone numbers */}
            <div className="flex flex-col space-y-2">
              <div className="py-2">
                <h2 className={cn(aldineBT.className, "text-2xl font-bold")}>
                  Give Us A Call
                </h2>
              </div>
              <div>
                <Link href="tel:0243482969" className="">
                  {" "}
                  +233 243 482 969
                </Link>
              </div>
              <div>
                <Link href="tel:0507646550" className="">
                  {" "}
                  +233 507 646 550
                </Link>
              </div>
            </div>
            {/* email */}
            <div className="flex flex-col space-y-2">
              <div className="py-2">
                <h2 className={cn(aldineBT.className, "text-2xl font-bold")}>
                  Send Us An Email
                </h2>
              </div>
              <div>
                <Link href="mailto:<EMAIL>">
                  <EMAIL>
                </Link>
              </div>
            </div>
            {/* email */}
            <div className="flex flex-col space-y-2">
              <div className="py-2">
                <h2 className={cn(aldineBT.className, "text-2xl font-bold")}>
                  Openening Hours
                </h2>
              </div>
              <div>
                <p>Monday to Saturday 9-8pm</p>
              </div>
            </div>
            {/* locate us */}
            <div className="flex w-full flex-col items-center space-y-2 md:items-start">
              <div className="py-2">
                <h2 className={cn(aldineBT.className, "text-2xl font-bold")}>
                  Locate Us
                </h2>
              </div>
              <div className="flex w-full flex-col items-center space-y-2">
                <div className="w-full rounded-3xl bg-gray-100 p-2">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2603.215846778419!2d-0.16302275279083636!3d5.634363174265053!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xfdf99b9ed152285%3A0xbc92f26b59b5ba39!2sBlackcherry%20Gh!5e0!3m2!1sen!2sgh!4v1708134876463!5m2!1sen!2sgh"
                    width="100%"
                    height="280"
                    loading="lazy"
                    className="w-full rounded-2xl"
                  ></iframe>
                </div>
              </div>
            </div>
          </div>
          <div>
            <ContactForm />
          </div>
        </div>
      </div>
    </div>
  );
}
