import Footer from "@/components/footer";
import Navbar from "@/components/layouts/navbar";
import { buttonVariants } from "@/components/ui/button";
import RateCard from "@/components/ux/rate-card";
import { aldineBT } from "@/constants/fonts";
import { pricing_hero } from "@/constants/images";
import { getRateCards } from "@/lib/sanity/lib/actions";
import { cn } from "@/lib/utils";
import { rateCardType } from "@/types/types";
import { ChevronRight } from "lucide-react";
import { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";

export const revalidate = 30;

export const metadata: Metadata = {
  title: "Bridal Bookings Ratecard",
  description: "Our Ratecard, Tailored with Care for Our Clients' Needs.",
  openGraph: {
    title: "Bridal Bookings Ratecard | Black Cherry",
    description:
      "Our Bridal Bookings Ratecard, Tailored with Care for Our Clients' Needs.",
    url: "https://www.blackcherrygh.com/bridal-bookings-ratecard/",
    siteName: "Bridal Bookings Ratecard | Black Cherry",
    locale: "en-US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Bridal Bookings Ratecard | Black Cherry",
    description: "Our Ratecard, Tailored with Care for Our Clients' Needs.",
    site: "https://www.blackcherrygh.com/bridal-bookings-ratecard/",
  },
};

export default async function Page() {
  const rateCard: rateCardType[] = await getRateCards();
  // console.log(rateCard);
  const packages = rateCard.filter((r) => r.ratetype === "package");
  const singles = rateCard.filter((r) => r.ratetype === "single");

  return (
    <main>
      <Navbar />
      <div>
        <section className="flex w-full flex-col items-center">
          <div className="flex w-full max-w-7xl flex-col items-center px-4">
            <div className="relative z-10 mt-20 mb-8 flex h-[40rem] w-full flex-col items-center justify-center overflow-hidden rounded-3xl bg-gray-50">
              <div className="relative">
                <div className="absolute inset-0 bg-black/60" />
                <Image
                  alt="spa"
                  src={pricing_hero}
                  className="h-[40rem] object-cover"
                />
              </div>
              <div className="absolute bottom-4 flex h-full w-full max-w-lg flex-col items-center justify-end p-4 text-center">
                <div className="space-y-2 text-white">
                  <h2 className="text-5xl">Bridal Bookings Ratecard</h2>
                  <p>Bridal Beauty, Rates for Your Special Day</p>
                  <div className="pt-4">
                    <Link
                      href={"/"}
                      className={cn(
                        buttonVariants({
                          variant: "default",
                          size: "withIconRight",
                        }),
                        "group space-x-2",
                      )}
                    >
                      <span>Home</span>
                      <div className="flex items-center justify-center pl-2">
                        <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                        <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <section className="mt-8 flex w-full flex-col items-center">
          {/* ratecards */}
          <div>
            <h2 className={cn(aldineBT.className, "text-4xl font-bold")}>
              PACKAGE RATES
            </h2>
          </div>
          <div className="mt-10 flex h-full w-full max-w-7xl flex-col items-center space-y-4 p-4">
            {/* packages */}
            <div className="grid h-full w-full gap-3 md:grid-cols-3">
              {packages.map((p) => {
                return (
                  <div key={p._id}>
                    <RateCard rate={p} />
                  </div>
                );
              })}
            </div>
            <div className="flex w-full flex-col items-center text-center">
              <p className="mb-10 w-full text-gray-400">
                All above packages are for same day events!
              </p>
            </div>
          </div>
          <div className="flex h-full w-full max-w-2xl flex-col items-center space-y-10 p-4">
            <div>
              <h2 className={cn(aldineBT.className, "text-4xl font-bold")}>
                SINGLE RATES
              </h2>
            </div>
            {/* singles */}
            <div className="grid w-full gap-4">
              {singles.map((p) => {
                return (
                  <div key={p._id}>
                    <RateCard rate={p} />
                  </div>
                );
              })}
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </main>
  );
}
