import Footer from "@/components/footer";
import ReviewsForm from "@/components/forms/reviews/review-form";
import Navbar from "@/components/layouts/navbar";
import ShareBanner from "@/components/share-banner";
import { aldineBT } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "New Review",
  description: "Leave A Review.",
  openGraph: {
    title: "New Review | Black Cherry",
    description: "Leave A Review.",
    url: "https://www.blackcherrygh.com/reviews/new/",
    siteName: "New Review | Black Cherry",
    locale: "en-US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "New Review | Black Cherry",
    description: "Leave A Review.",
    site: "https://www.blackcherrygh.com/reviews/new/",
  },
};

export default function Page() {
  return (
    <main className=''>
      <Navbar />
      <div className='mx-auto mt-24 flex w-full max-w-96 flex-col p-4'>
        <section className='flex w-full flex-col items-center space-y-2 p-4 text-center'>
          {/* top banner */}
          <ShareBanner />
          <h2 className={cn(aldineBT.className, "text-4xl md:text-6xl")}>
            Leave Your Review
          </h2>
          <p className='text-neutral-500'>Tell Us What Your Experience</p>
        </section>
        <section className='flex w-full flex-col'>
          <ReviewsForm />
        </section>
      </div>
      <Footer />
    </main>
  );
}
