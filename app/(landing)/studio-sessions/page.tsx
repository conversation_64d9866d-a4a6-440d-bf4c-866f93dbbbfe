import { buttonVariants } from "@/components/ui/button";
import { aldineBT } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import { ChevronRight } from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";

export const revalidate = 30;

export const metadata: Metadata = {
  title: "Studio Sessions",
  description:
    "Professional makeup services for photoshoots, studio sessions, and home visits. Transform your look with our expert makeup artistry.",
  openGraph: {
    title: "Studio Sessions | Black Cherry",
    description:
      "Professional makeup services for photoshoots, studio sessions, and home visits. Transform your look with our expert makeup artistry.",
    url: "https://www.blackcherrygh.com/studio-sessions/",
    siteName: "Studio Sessions | Black Cherry",
    locale: "en-US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Studio Sessions | Black Cherry",
    description:
      "Professional makeup services for photoshoots, studio sessions, and home visits. Transform your look with our expert makeup artistry.",
    site: "https://www.blackcherrygh.com/studio-sessions/",
  },
};

// Service data based on the pricing image
const services = [
  {
    id: 1,
    title: "Photoshoot (Location)",
    description: "Professional makeup for photoshoots at your chosen location",
    pricing: [
      { duration: "1 hour 30 Min", price: 1200 }, 
      { duration: "2-4 hours", price: 1600 }, 
      { duration: "4-6 hours", price: 2500 },
    ],
    type: "photoshoot",
  },
  {
    id: 2,
    title: "Studio Walk-in (Studio Artist)",
    description:
      "Professional makeup services at our studio with our skilled artists",
    pricing: [{ duration: "Session", price: 400 }],
    type: "studio",
  },
  {
    id: 3,
    title: "Studio Walk-in (Cherry)",
    description:
      "Premium makeup services at our studio with Cherry, our lead artist",
    pricing: [{ duration: "Session", price: 750 }],
    type: "studio-premium",
  },
  {
    id: 4,
    title: "Home Service (Within Accra)",
    description: "Convenient makeup services at your home within Accra",
    pricing: [
      { duration: "2-3 hours", price: "1200-1500" },
      { duration: "Extra hour", price: 100 },
    ],
    type: "home-service",
  },
];

const policies = [
  "To hold an appointment kindly make a deposit of 50-100%",
  "Without a deposit appointment does not hold",
  "In case of cancellation kindly alert us 48hrs prior to appointment for 50% refund",
];

export default function Page() {
  return (
    <main>
      <div>
        {/* Hero Section */}
        <section className="flex w-full flex-col items-center">
          <section className="flex w-full max-w-7xl flex-col items-center px-4">
            <div className="relative z-10 mt-20 mb-8 flex h-[40rem] w-full flex-col items-center justify-center overflow-hidden rounded-3xl bg-gradient-to-br from-pink-100 via-white to-pink-50">
              {/* Background pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgb(219_39_119)_1px,transparent_0)] bg-[length:24px_24px]" />
              </div>

              <div className="absolute bottom-4 flex h-full w-full max-w-lg flex-col items-center justify-end p-4 text-center">
                <div className="flex flex-col items-center space-y-4">
                  {/* Logo/Brand */}
                  {/* <div className="mb-4">
                    <h1
                      className={cn(
                        aldineBT.className,
                        "text-6xl font-bold text-pink-600",
                      )}
                    >
                      BLACK
                    </h1>
                    <h1
                      className={cn(
                        aldineBT.className,
                        "-mt-2 text-6xl font-bold text-pink-600",
                      )}
                    >
                      CHERRY
                    </h1>
                  </div> */}

                  <h2
                    className={cn(
                      aldineBT.className,
                      "text-3xl font-bold text-gray-900",
                    )}
                  >
                    Studio Sessions
                  </h2>
                  <p className="max-w-md text-gray-600">
                    Transform your look with our professional makeup services.
                    Whether it&apos;s for your special day, a photoshoot, or any
                    important occasion.
                  </p>
                  <div className="pt-4">
                    <Link
                      href="/contact"
                      className={cn(
                        buttonVariants({
                          variant: "default",
                          size: "withIconRight",
                        }),
                        "group space-x-2",
                      )}
                    >
                      <span>Book Now</span>
                      <div className="flex items-center justify-center pl-2">
                        <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                        <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </section>

        {/* Services Section */}
        <section className="mt-8 flex w-full flex-col items-center">
          <div className="w-full max-w-7xl px-4">
            <div className="mb-12 text-center">
              <h2
                className={cn(
                  aldineBT.className,
                  "mb-4 text-4xl font-bold text-gray-900",
                )}
              >
                Our Services
              </h2>
              <p className="mx-auto max-w-2xl text-gray-600">
                Choose from our range of professional makeup services designed
                to enhance your natural beauty
              </p>
            </div>

            {/* Service Cards */}
            <div className="mb-12 grid gap-6 md:grid-cols-2 lg:grid-cols-2">
              {services.map((service) => (
                <div
                  key={service.id}
                  className="flex flex-col rounded-3xl border bg-white p-6 shadow-sm transition-shadow hover:shadow-md"
                >
                  <div className="flex-1">
                    <h3
                      className={cn(
                        aldineBT.className,
                        "mb-2 text-2xl font-bold text-gray-900",
                      )}
                    >
                      {service.title}
                    </h3>
                    <p className="mb-4 text-gray-600">{service.description}</p>

                    <div className="space-y-2">
                      {service.pricing.map((price, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between border-b border-gray-100 py-2 last:border-b-0"
                        >
                          <span className="text-gray-700">
                            {price.duration}
                          </span>
                          <span
                            className={cn(
                              aldineBT.className,
                              "text-lg font-bold text-pink-600",
                            )}
                          >
                            GH₵{price.price}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Payment Details */}
            <div className="mb-12 rounded-3xl bg-gray-50 p-8">
              <div className="mb-6 text-center">
                <h3
                  className={cn(
                    aldineBT.className,
                    "mb-2 text-2xl font-bold text-gray-900",
                  )}
                >
                  Payment Details
                </h3>
                <p className="text-gray-600">Merchant ID: 773198</p>
                <p className="text-gray-600">Blackcherry</p>
              </div>
            </div>

            {/* Policies */}
            <div className="rounded-3xl bg-pink-50 p-8 mb-20">
              <h3
                className={cn(
                  aldineBT.className,
                  "mb-6 text-center text-2xl font-bold text-gray-900",
                )}
              >
                Booking Policies
              </h3>
              <div className="space-y-4">
                {policies.map((policy, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-pink-600" />
                    <p className="text-gray-700">{policy}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>
  );
}
