import { buttonVariants } from "@/components/ui/button";
import { FounderProfile } from "@/components/ux/about/founder-profile";
import { Noise } from "@/components/ux/animations/noise";
import { ParallaxImage } from "@/components/ux/animations/parallax-image";
import { ProgressiveBlur } from "@/components/ux/animations/progressive-blur";
import { EventsCarousel } from "@/components/ux/home/<USER>";
import { aldineBT } from "@/constants/fonts";
import { class_3, sn_18, sn_Logo } from "@/constants/images";
import { getCVData } from "@/lib/cv-content";
import { cn } from "@/lib/utils";
import ccLogo from "@/public/cc_logo.png";
// import aL from "@/public/images/pc-l.png";
import aL from "@/public/images/wlc-pge.jpg";
import { ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export const revalidate = 30;

export default async function Home() {
  // const imageComponent: imageComponent =
  //   await getImageComponentBySlug("home-carousel");

  // const seyNaturelleProducts: seyNaturelleProductdType[] =
  //   await getAllSeyNaturelleProducts();
  const cv = getCVData();
  const diyClass2023 =
    "https://ndaacfwbggkjxtjwacrh.supabase.co/storage/v1/object/public/videos//BlackCherry-DIY-class-2023.MP4";
  return (
    <main>
      <section className="relative z-10 flex h-[44rem] w-full flex-col items-center justify-center overflow-hidden md:h-[94dvh] lg:h-[100dvh]">
        <Noise />
        <Image
          alt="home"
          src={aL}
          priority
          className="absolute inset-0 z-0 h-[44rem] object-cover object-[80%] md:-mt-44 md:h-[150dvh] md:object-top xl:-mt-60"
        />
        <ProgressiveBlur
          className="absolute inset-0 bg-black/50"
          direction="bottom"
          blurLayers={4}
          blurIntensity={0.2}
        />
        <div className="relative z-50 mx-auto flex h-[44rem] w-full max-w-5xl flex-col justify-end p-4">
          <div className="relative flex max-w-md flex-col space-y-2 py-4">
            <h1
              className={cn(
                aldineBT.className,
                "text-4xl text-white md:text-6xl",
              )}
            >
              {/* {cv.personalInfo.name} */}
              Welcome
            </h1>
            <p className={cn("text-white/80")}>
              to our world of timeless beauty…. We&apos;re excited to be a part
              of your special day and we cannot wait to Glam you like the queen
              you are!
            </p>
            <div className="pt-4">
              <Link
                href={"/contact"}
                className={cn(
                  buttonVariants({
                    size: "withIconRight",
                  }),
                  "group space-x-2 font-bold",
                )}
              >
                <span> Book Your Consultation</span>
                <div className="flex items-center justify-center pl-2">
                  <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                  <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                </div>
              </Link>
            </div>
          </div>
        </div>
      </section>
      <FounderProfile />
      {/* ytvideo parallax with no content */}
      {/* <section className="relative z-10 h-[14rem] w-full overflow-hidden md:h-[33rem]">
        <ProgressiveBlur
          className="absolute inset-0 z-20 bg-black/70"
          direction="bottom"
          blurLayers={6}
          blurIntensity={0.5}
        />
        <ParallaxVideo
          videoUrl={diyClass2023}
          className=""
          height="h-[14rem] md:h-[44rem]"
        />
      </section> */}
      {/* video carousel */}
      <EventsCarousel />
      {/* parallax image */}
      <section className="relative z-10 hidden h-[33rem] w-full overflow-hidden">
        <ProgressiveBlur
          className="absolute inset-0 z-20 bg-black/70"
          direction="bottom"
          blurLayers={6}
          blurIntensity={0.5}
        />
        <ParallaxImage
          className=""
          height="h-[44rem]"
          // parallaxIntensity={0.3}
        />
      </section>
      {/* sey naturelle */}
      <section className="flex flex-col items-center px-4 pt-10">
        <div className="relative z-10 mx-auto mb-10 flex h-[28rem] w-full max-w-5xl flex-col items-center justify-center overflow-hidden rounded-3xl bg-gray-50">
          <div className="relative">
            <div className="absolute inset-0 bg-black/70" />
            <Image
              alt="sey_naturelle"
              src={sn_18}
              className="h-[40rem] object-cover"
            />
          </div>
          <div className="absolute bottom-2 flex h-full w-full max-w-lg flex-col items-center justify-end p-4 text-center">
            <div className="flex h-full w-full flex-col items-center justify-center">
              <div>
                <Image
                  alt="sey_naturelle_logo"
                  src={sn_Logo}
                  className="w-36"
                />
              </div>
              <div className="-mt-8 flex w-full max-w-lg flex-col items-center p-4 text-center">
                <h2
                  className={cn(
                    aldineBT.className,
                    "pb-2 text-4xl font-medium text-white",
                  )}
                >
                  Sey Naturelle
                </h2>
                <p className="text-white/80">
                  Sey Naturelle is a chemical free cosmetic line carefully
                  formulated with Ayurveda herbs tried and tested to provide
                  exceptional benefits to wellbeing of the skin.
                </p>
                <div className="mt-2 flex items-center space-x-2">
                  <Link
                    href={"/sey-naturelle"}
                    className={cn(
                      buttonVariants({
                        variant: "default",
                        size: "withIconRight",
                      }),
                      "group space-x-2 bg-[#C9EF50] hover:bg-[#C9EF50]",
                    )}
                  >
                    <span>View Sey Naturelle</span>
                    <div className="flex items-center justify-center pl-2">
                      <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                      <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                    </div>
                  </Link>
                  <Link
                    href={"/store#sey-nanturelle"}
                    className={cn(
                      buttonVariants({
                        variant: "default",
                        size: "withIconRight",
                      }),
                      "group space-x-2",
                    )}
                  >
                    <span>Browse In Shop</span>
                    <div className="flex items-center justify-center pl-2">
                      <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                      <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* cc */}
      <section className="flex flex-col items-center px-4 pt-10">
        <div className="relative z-10 mx-auto mb-10 flex h-[40rem] w-full max-w-5xl flex-col items-center justify-center overflow-hidden rounded-3xl bg-gray-50">
          <div className="relative">
            <div className="absolute inset-0 bg-black/70" />
            {/* <Image
              alt="sey_naturelle"
              src={sn_18}
              className="h-[40rem] object-cover"
            /> */}
            <video
              autoPlay={true}
              muted
              playsInline
              loop
              preload="metadata"
              aria-label="video player"
              className="h-[40rem] w-full object-cover object-[50%_20%]"
            >
              <source src={"/all-lipstics.mp4"} type="video/mp4" />
              Your browser doesnot support video tag
            </video>
          </div>
          <div className="absolute bottom-2 flex h-full w-full max-w-lg flex-col items-center justify-end p-4 text-center">
            <div className="flex h-full w-full flex-col items-center justify-center">
              <div>
                <Image alt="sey_naturelle_logo" src={ccLogo} className="w-36" />
              </div>
              <div className="-mt-8 flex w-full max-w-lg flex-col items-center p-4 text-center">
                <h2
                  className={cn(
                    aldineBT.className,
                    "pb-2 text-4xl font-medium text-white",
                  )}
                >
                  Covered Cosmetics
                </h2>
                <p className="text-white/80">Confidence Looks Good On You.</p>
                <div className="mt-2 flex items-center space-x-2">
                  <Link
                    href={"https://www.coveredcosmetic.com/"}
                    className={cn(
                      buttonVariants({
                        size: "withIconRight",
                      }),
                      "group space-x-2]",
                    )}
                  >
                    <span>View Covered Cosmetics</span>
                    <div className="flex items-center justify-center pl-2">
                      <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                      <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="relative z-10 flex h-[44rem] w-full flex-col items-center justify-center overflow-hidden">
        <Noise />
        <Image
          alt="home"
          src={class_3}
          className="absolute inset-0 z-0 h-[44rem] object-cover object-[80%] md:object-left"
        />
        <ProgressiveBlur
          className="absolute inset-0 bg-black/70"
          direction="bottom"
          blurLayers={6}
          blurIntensity={0.5}
        />
        <div className="relative z-50 mx-auto flex h-[44rem] w-full max-w-5xl flex-col justify-end p-4">
          <div className="relative flex max-w-lg flex-col space-y-2 py-4">
            <h1
              className={cn(
                aldineBT.className,
                "text-4xl text-white md:text-6xl",
              )}
            >
              SCHEDULE YOUR MAKEUP SESSION TODAY
            </h1>
            <p className={cn("text-white/80")}>
              {" "}
              Transform your look with our professional makeup services. Whether
              it&apos;s for your special day, a photoshoot, or any important
              occasion, let us enhance your natural beauty with our expert
              makeup artistry and premium products.
            </p>
            <div className="pt-4">
              <Link
                href={"/studio-sessions"}
                className={cn(
                  buttonVariants({
                    size: "withIconRight",
                  }),
                  "group space-x-2 font-bold",
                )}
              >
                <span>Book Your Consultation</span>
                <div className="flex items-center justify-center pl-2">
                  <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                  <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                </div>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
