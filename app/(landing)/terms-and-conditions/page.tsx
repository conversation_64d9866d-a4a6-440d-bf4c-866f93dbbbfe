import Description from "@/components/studio/post-body";
import { buttonVariants } from "@/components/ui/button";
import { terms_hero } from "@/constants/images";
import { getTerms } from "@/lib/sanity/lib/actions";
import { cn } from "@/lib/utils";
import { termsType } from "@/types/types";
import { ChevronRight } from "lucide-react";
import { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";

export const revalidate = 30;

export const metadata: Metadata = {
  title: "Terms and Conditions",
  description:
    "Cosmetic Consultancy, Beauty Services, Training Services on Cosmetic.",
  openGraph: {
    title: "Terms and Conditions | Black Cherry",
    description:
      "Cosmetic Consultancy, Beauty Services, Training Services on Cosmetic.",
    url: "https://www.blackcherrygh.com/terms-and-conditions/",
    siteName: "Terms and Conditions | Black Cherry",
    locale: "en-US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Terms and Conditions | Black Cherry",
    description:
      "Cosmetic Consultancy, Beauty Services, Training Services on Cosmetic.",
    site: "https://www.blackcherrygh.com/terms-and-conditions/",
  },
};

export default async function Page() {
  const terms: termsType = await getTerms();
  // console.log(terms);
  return (
    <main>
      <div>
        {/* hero section */}
        <section className="flex w-full flex-col items-center">
          <section className="flex w-full max-w-7xl flex-col items-center px-4">
            <div className="relative z-10 mt-20 mb-8 flex h-[40rem] w-full flex-col items-center justify-center overflow-hidden rounded-3xl bg-gray-50">
              <div className="relative">
                <div className="absolute inset-0 bg-black/60" />
                <Image
                  alt="spa"
                  src={terms_hero}
                  className="h-[40rem] object-cover"
                />
              </div>
              <div className="absolute bottom-4 flex h-full w-full max-w-lg flex-col items-center justify-end p-4 text-center">
                <div className="space-y-2 text-white">
                  <h2 className="text-5xl">Terms And Conditions</h2>
                  <p>Our terms and conditions</p>
                  <div className="pt-4">
                    <Link
                      href={"/contact"}
                      className={cn(
                        buttonVariants({
                          variant: "default",
                          size: "withIconRight",
                        }),
                        "group space-x-2",
                      )}
                    >
                      <span>Contact Us</span>
                      <div className="flex items-center justify-center pl-2">
                        <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                        <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </section>
        {/* this is terms page */}
        <section className="flex w-full flex-col items-center">
          <div className="flex w-full max-w-xl flex-col items-center p-4">
            <Description
              content={terms.description}
              className="text-center text-lg tracking-wide"
            />
          </div>
        </section>
      </div>
    </main>
  );
}
