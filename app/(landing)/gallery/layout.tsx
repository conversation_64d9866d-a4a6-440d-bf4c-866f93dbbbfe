import { buttonVariants } from "@/components/ui/button";
import GalleryPageSwitchToggle from "@/components/ux/gallery/page-switch-toggle";
import { gallery_hero } from "@/constants/images";
import { getGalleryCategories } from "@/lib/sanity/lib/actions";
import { cn } from "@/lib/utils";
import { ChevronRight } from "lucide-react";
import { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";

export const revalidate = 30;

export const metadata: Metadata = {
  title: "Gallery",
  description:
    "Discover Beauty in Every Detail, Browse Our Gallery of Stunning Creations.",
  openGraph: {
    title: "Gallery | Black Cherry",
    description:
      "Discover Beauty in Every Detail, Browse Our Gallery of Stunning Creations.",
    url: "https://www.blackcherrygh.com/gallery/",
    siteName: "Gallery | Black Cherry",
    locale: "en-US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Gallery | Black Cherry",
    description:
      "Discover Beauty in Every Detail, Browse Our Gallery of Stunning Creations.",
    site: "https://www.blackcherrygh.com/gallery/",
  },
};

export default async function GalleryLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const categories = await getGalleryCategories();
  return (
    <main>
      <div>
        {/* hero section */}
        <section className="flex w-full flex-col items-center">
          <section className="flex w-full max-w-7xl flex-col items-center px-4">
            <div className="relative z-10 mt-20 mb-8 flex h-[40rem] w-full flex-col items-center justify-center overflow-hidden rounded-3xl bg-gray-50">
              <div className="relative">
                <div className="absolute inset-0 bg-black/60" />
                <Image
                  alt="spa"
                  src={gallery_hero}
                  className="h-[40rem] object-cover"
                />
              </div>
              <div className="absolute bottom-4 flex h-full w-full max-w-lg flex-col items-center justify-end p-4 text-center">
                <div className="space-y-2 text-white">
                  <h2 className="text-5xl">Gallery</h2>
                  <p>Reach Out and Connect with Blackcherry</p>
                  <div className="pt-4">
                    <Link
                      href={"/"}
                      className={cn(
                        buttonVariants({
                          variant: "default",
                          size: "withIconRight",
                        }),
                        "group space-x-2",
                      )}
                    >
                      <span>Home</span>
                      <div className="flex items-center justify-center pl-2">
                        <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                        <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </section>
        {/* page switch toggle */}
        <GalleryPageSwitchToggle categories={categories} />
        {/* pages */}
        <section>{children}</section>
      </div>
    </main>
  );
}
