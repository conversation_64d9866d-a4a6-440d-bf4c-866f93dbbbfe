import { ImageModal } from "@/components/studio/img-modal";
import { getGalleryBySlug, getGallerySlugs } from "@/lib/sanity/lib/actions";
import { galleryType } from "@/types/types";
import { Metadata } from "next";

export const revalidate = 180;

async function getData(slug: string) {
  return await getGalleryBySlug(slug);
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const slug = (await params).slug;
  const galleryImgs: galleryType[] = await getData(slug);
  const newTitle = galleryImgs[0];
  const title = newTitle?.imageName;
  const slugName = newTitle?.slug;

  return {
    title: `${title} - Blackcherry`,
    description:
      "Cosmetic Consultancy, Beauty Services, Training services on Cosmetic",
    openGraph: {
      title: `${title} | Black Cherry`,
      description:
        "Cosmetic Consultancy, Beauty Services, Training Services on Cosmetic.",
      url: `https://www.blackcherrygh.com/gallery/${slugName}`,
      siteName: `${title} | Black Cherry`,
      locale: "en-US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: `${title} | Black Cherry`,
      description:
        "Cosmetic Consultancy, Beauty Services, Training Services on Cosmetic.",
      site: `https://www.blackcherrygh.com/gallery/${slugName}`,
    },
  };
}

export async function generateStaticParams() {
  const gallerySlugs = await getGallerySlugs();
  return gallerySlugs.map((slug: string) => {
    return {
      slug,
    };
  });
}

export default async function Page({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const slug = (await params).slug;
  const galleryImgs: galleryType[] = await getData(slug);
  const hsone = galleryImgs[0].images;
  const hstwo = galleryImgs[0].secondimages;

  if (hsone && !hstwo) {
    return (
      <section className="flex w-full flex-col items-center">
        <div className="flex w-full max-w-6xl flex-col items-center p-4">
          <div className="flex w-full flex-col">
            <div className="grid w-full max-w-6xl grid-cols-2 gap-6 md:grid-cols-4">
              {hsone.map((img: any, index: number) => {
                return (
                  <div key={index}>
                    <ImageModal image={img} />
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>
    );
  } else if (!hsone && hstwo) {
    return (
      <section className="flex w-full flex-col items-center">
        <div className="flex w-full max-w-6xl flex-col items-center p-4">
          <div className="flex w-full flex-col">
            <div className="grid w-full max-w-6xl grid-cols-2 gap-6 md:grid-cols-4">
              {hstwo.map((img: any, index: number) => {
                return (
                  <div key={index}>
                    <ImageModal image={img} />
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>
    );
  } else if (hsone && hstwo) {
    const homegallery = [...hsone, ...hstwo];
    return (
      <section className="flex w-full flex-col items-center">
        <div className="flex w-full max-w-6xl flex-col items-center p-4">
          <div className="flex w-full flex-col">
            <div className="grid w-full max-w-6xl grid-cols-2 gap-6 md:grid-cols-4">
              {homegallery.map((img: any, index: number) => {
                return (
                  <div key={index}>
                    <ImageModal image={img} />
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="flex w-full flex-col items-center">
      <div className="flex w-full max-w-6xl flex-col items-center p-4">
        <div className="flex w-full flex-col">
          <div className="grid w-full max-w-6xl grid-cols-2 gap-6 md:grid-cols-4">
            images coming soon!
          </div>
        </div>
      </div>
    </section>
  );
}
