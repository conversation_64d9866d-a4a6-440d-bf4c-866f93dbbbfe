import ImageComponent from "@/components/studio/img";
import Description from "@/components/studio/post-body";
import { AddToCart } from "@/components/ux/store/add-to-cart";
import { aldineBT } from "@/constants/fonts";
import { currency } from "@/hooks/use-currency";
import {
  getAllStoreProducts,
  getStoreProductBySlug,
} from "@/lib/sanity/lib/actions";
import { cn } from "@/lib/utils";
import { seyNaturelleProductdType } from "@/types/types";
import { ArrowLeft, ShoppingBasket } from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";

export const revalidate = 30;

async function getProduct(slug: string) {
  return await getStoreProductBySlug(slug);
}
async function getSeyNaturelleProducts() {
  return await getAllStoreProducts();
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const slug = (await params).slug;
  const product: seyNaturelleProductdType = await getProduct(slug);
  const title = product.packageName;
  const slugName = product.slug;

  return {
    title: `${title} | Store`,
    description:
      "Cosmetic Consultancy, Beauty Services, Training services on Cosmetic",
    openGraph: {
      title: `${title} | Black Cherry`,
      description:
        "Cosmetic Consultancy, Beauty Services, Training Services on Cosmetic.",
      url: `https://www.blackcherrygh.com/store/${slugName}`,
      siteName: `${title} | Black Cherry`,
      locale: "en-US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: `${title} | Black Cherry`,
      description:
        "Cosmetic Consultancy, Beauty Services, Training Services on Cosmetic.",
      site: `https://www.blackcherrygh.com/store/${slugName}`,
    },
  };
}

export async function generateStaticParams() {
  const gallerySlugs: seyNaturelleProductdType[] =
    await getSeyNaturelleProducts();
  return gallerySlugs.map((data) => {
    return data.slug;
  });
}

export default async function Page({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const slug = (await params).slug;
  const product: seyNaturelleProductdType = await getProduct(slug);
  return (
    <div className="flex w-full flex-col items-center">
      <section className="flex w-full flex-col items-center">
        <div className="flex w-full max-w-6xl flex-col items-center px-4 pt-10">
          <div className="flex w-full items-start py-2">
            <Link href={"/store"} className="flex items-center space-x-2">
              <span className="flex items-center">
                <ArrowLeft className="h-4 w-4" />
              </span>
              <span>Go to Store</span>
            </Link>
          </div>
          <div className="grid gap-4 pb-10 md:grid-cols-9">
            {/* product details */}
            <div className="border-accent-100/20 relative h-full rounded-3xl border bg-white p-2 md:col-span-5">
              <div className="flex items-center justify-between">
                <p className="rounded-full bg-white px-3 py-2 text-sm font-medium">
                  Store Product
                </p>
                <p className="px-2">
                  <ShoppingBasket className="h-5 w-5" />
                </p>
              </div>
              {/* images */}
              <ImageComponent
                image={product.coverImage}
                className="rounded-xl object-cover md:h-[42rem]"
              />
              {/* details */}
              <div className="p-4">
                <h2
                  className={cn(
                    aldineBT.className,
                    "mb-2 px-2 text-4xl font-bold",
                  )}
                >
                  {product.packageName}
                </h2>
                <p className="text-accent-100/50 px-2 text-base">
                  {currency(product.price, "GHS")}
                </p>
              </div>
              {/* descriptions */}
              <div className="border-accent-100/20 border-t p-4">
                <Description content={product.features} />
              </div>
              {/* support */}
              <div className="border-accent-100/20 flex flex-col space-y-2 border-t p-4">
                <span>For More Info Cutact Us At.</span>
                <Link
                  href={`#`}
                  className="opacity-60 transition-all duration-300 ease-linear hover:opacity-100"
                >
                  <span><EMAIL></span>
                </Link>
              </div>
            </div>
            {/* check out */}
            <div className="md:col-span-4">
              <div className="border-accent-100/20 sticky top-20 rounded-3xl border bg-white p-2">
                {product.stock === "out-of-stock" ? (
                  <div className="flex flex-col items-center space-y-4">
                    <h2 className="pt-4 pl-4 text-2xl font-bold">
                      Product Is Out Of Stock
                    </h2>
                    <Link
                      href={"/store"}
                      className="w-fit rounded-full bg-black px-4 py-2 text-white"
                    >
                      <span>Back To Store</span>
                    </Link>
                  </div>
                ) : (
                  // <ProductPurchase product={product} />
                  <AddToCart product={product} />
                )}
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
