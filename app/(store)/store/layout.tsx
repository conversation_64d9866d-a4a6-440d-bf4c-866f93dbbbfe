import Footer from "@/components/footer";
import Navbar from "@/components/layouts/navbar";
import { buttonVariants } from "@/components/ui/button";
import { CartPopup } from "@/components/ux/store/cart-popup";
import { sn_17 } from "@/constants/images";
import { CartStoreProvider } from "@/constants/store/cart";
import { cn } from "@/lib/utils";
import { ChevronRight } from "lucide-react";
import { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";

export const revalidate = 30;

export const metadata: Metadata = {
  title: "Store",
  description: "Discover Your Beauty Essentials, Explore Our Store",
  openGraph: {
    title: "Store | Black Cherry",
    description: "Discover Your Beauty Essentials, Explore Our Store",
    url: "https://www.blackcherrygh.com/store/",
    siteName: "Store | Black Cherry",
    locale: "en-US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Store | Black Cherry",
    description: "Discover Your Beauty Essentials, Explore Our Store",
    site: "https://www.blackcherrygh.com/store/",
  },
};

export default function ContactLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <main className="relative">
      <Navbar />
      <div>
        {/* hero section */}
        <section className="flex w-full flex-col items-center">
          <section className="flex w-full max-w-7xl flex-col items-center px-4">
            <div className="relative z-10 mb-8 mt-20 flex h-[40rem] w-full flex-col items-center justify-center overflow-hidden rounded-3xl bg-gray-50">
              <div className="relative">
                <div className="absolute inset-0 bg-black/60" />
                <Image
                  alt="spa"
                  src={sn_17}
                  className="h-[40rem] object-cover"
                />
              </div>
              <div className="absolute bottom-4 flex h-full w-full max-w-lg flex-col items-center justify-end p-4 text-center">
                <div className="space-y-2 text-white">
                  <h2 className="text-5xl">Store</h2>
                  <p>Discover Your Beauty Essentials, Explore Our Store</p>
                  <div className="pt-4">
                    <Link
                      href={"/"}
                      className={cn(
                        buttonVariants({
                          variant: "default",
                          size: "withIconRight",
                        }),
                        "group space-x-2",
                      )}
                    >
                      <span>Home</span>
                      <div className="flex items-center justify-center pl-2">
                        <div className="-mr-3 mt-[0.8px] h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                        <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </section>
        {/* pages */}
        <CartStoreProvider>
          <section>{children}</section>
          <CartPopup />
        </CartStoreProvider>
      </div>
      <Footer />
    </main>
  );
}
