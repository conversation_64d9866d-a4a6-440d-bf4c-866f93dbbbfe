"use client";

export default function Error({ reset }: { reset: () => void }) {
  return (
    <div className="mx-auto my-40 flex max-w-xl flex-col">
      <h2 className="text-xl font-bold">Oh no!</h2>
      <p className="my-2">There is an error</p>
      <button
        className="mx-auto mt-4 flex w-full items-center justify-center rounded-full bg-blue-600 p-4 tracking-wide text-white hover:opacity-90"
        onClick={() => reset()}
      >
        Try Again
      </button>
    </div>
  );
}
