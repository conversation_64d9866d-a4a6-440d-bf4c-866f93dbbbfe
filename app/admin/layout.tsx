import AdminNavbar from "@/components/admin/navbar";
import Footer from "@/components/footer";
import Navbar from "@/components/layouts/navbar";
import { supabaseServer } from "@/lib/supabase/server";
import { getAuthenticatedUser } from "@/lib/supabase/session";

export const dynamic = "force-dynamic";

export default async function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Use secure authentication method
  const user = await getAuthenticatedUser();

  if (!user) {
    return (
      <div>
        <Navbar />
        <div className="flex min-h-screen flex-col bg-gray-50">{children}</div>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Navbar />
      <div className="flex min-h-screen flex-col bg-gray-50">
        <div className="mt-20 flex w-full flex-col items-center">
          <div className="relative z-0 flex w-full max-w-7xl flex-col items-center justify-center px-4">
            <div className="flex w-full flex-col items-center">
              <div className="fixed inset-x-auto top-20">
                <AdminNavbar user={user} />
              </div>
              <div className="mt-16 flex w-full flex-col">{children}</div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
