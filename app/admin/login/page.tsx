import { cn } from "@/lib/utils";
import { redirect } from "next/navigation";
import { getAuthenticatedUser } from "@/lib/supabase/session";
import LoginForm from "@/components/forms/auth/login-form";
import { aldineBT } from "@/constants/fonts";

export const dynamic = "force-dynamic";

export default async function LogIn() {
  const user = await getAuthenticatedUser();

  if (user) {
    redirect("/admin/store");
  }

  // const state = searchParams.state;
  // const pass = searchParams.pass;
  // if (!state) {
  //   redirect("/dashboard/login?state=login");
  // }
  return (
    <main className="">
      <section className="flex h-full min-h-[48rem] w-full flex-col items-center bg-gray-50">
        {/* form */}
        <div className="flex w-full flex-1 flex-col items-center px-4">
          <div className="flex w-full flex-col items-center justify-center pt-2 pb-4">
            <div className="mt-20 flex w-full flex-col items-center justify-center text-center">
              <h2 className={cn(aldineBT.className, "py-4 text-2xl font-bold")}>
                Admin
              </h2>
            </div>
            {/* icon */}
            <div className="relative z-0 flex h-24 w-24 flex-col items-center justify-center overflow-hidden rounded-full border border-gray-200 bg-white">
              <div className="absolute inset-0 z-1 bg-linear-to-b from-transparent to-gray-100" />
              <div className="relative z-2 flex h-16 w-16 flex-col items-center justify-center rounded-full border border-gray-200 bg-white">
                <span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth="1.5"
                    stroke="currentColor"
                    className="h-8 w-8"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                    />
                  </svg>
                </span>
              </div>
            </div>
            {/* message */}
            <div className="space-y-1 text-center">
              <h2 className={cn(aldineBT.className, "text-2xl font-bold")}>
                Login to your account
              </h2>
              <p className="text-gray-400">Enter your details to login.</p>
            </div>
          </div>
          <LoginForm />
        </div>
      </section>
    </main>
  );
}
