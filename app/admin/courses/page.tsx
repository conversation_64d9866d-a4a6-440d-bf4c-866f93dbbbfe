import { ClassBackendTable } from "@/components/admin/course-table";
import { aldineBT } from "@/constants/fonts";
import { supabaseServer } from "@/lib/supabase/server";
import { getAuthenticatedUser } from "@/lib/supabase/session";
import { cn } from "@/lib/utils";
import { redirect } from "next/navigation";

export const dynamic = "force-dynamic";

export default async function Page() {
  const supabase = await supabaseServer();
  const user = await getAuthenticatedUser();

  if (!user) {
    redirect("/admin/login");
  }

  const { data, error } = await supabase.from("class_backend").select("*");
  console.log(error);
  return (
    <main className="min-h-[38rem] p-4">
      <div>
        <div className="flex w-full flex-col items-center">
          <div className="flex w-full max-w-xl flex-col p-4">
            <h2
              className={cn(
                aldineBT.className,
                "text-center text-4xl font-bold",
              )}
            >
              Courses
            </h2>
          </div>
        </div>
        <div>
          {error && !data ? (
            <div>error</div>
          ) : (
            <div>
              <ClassBackendTable data={data} />
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
