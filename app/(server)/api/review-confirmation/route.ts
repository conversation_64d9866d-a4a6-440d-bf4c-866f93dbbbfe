import { formSchema } from "@/components/forms/reviews/review-form";
import { ReviewEmail, ReviewEmailOwner } from "@/emails/review";
import { NextResponse } from "next/server";
import { Resend } from "resend";
import { z } from "zod";

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);
type Data = z.infer<typeof formSchema>;

export async function POST(request: Request) {
  const data: Data = await request.json();
  console.log("start");
  try {
    const owner = await resend.emails.send({
      from: "Blackcherry | Blackcherry <<EMAIL>>",
      to: ["<EMAIL>"],
      bcc: ["iamjulius<PERSON><EMAIL>"],
      subject: `${data.customer_name} Left A Review`,
      text: "",
      react: ReviewEmailOwner(data),
    });

    const client = await resend.emails.send({
      from: "Blackcherry | Blackcherry <<EMAIL>>",
      to: [data.customer_email],
      // bcc: ['iamju<PERSON><PERSON><PERSON>@gmail.com'],
      subject: "You Left A Review at Blackcherry",
      text: "",
      react: ReviewEmail(data),
    });
    console.log("end");

    return NextResponse.json({ owner, client });
  } catch (error) {
    console.log(error);
    return NextResponse.json({ error });
  }
}
