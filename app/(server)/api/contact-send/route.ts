import { contactformSchema } from "@/components/forms/contact/contact-form";
import {
  ContactConfirmationEmail,
  ContactConfirmationEmailOwner,
} from "@/emails/contact-confirmation";
import { NextResponse } from "next/server";
import { Resend } from "resend";
import { z } from "zod";

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

export async function POST(request: Request) {
  const Data: z.infer<typeof contactformSchema> = await request.json();
  console.log("start");
  try {
    const owner = await resend.emails.send({
      from: "Blackcherry | Blackcherry <<EMAIL>>",
      to: ["<EMAIL>"],
      bcc: ["<EMAIL>"],
      subject: `New ${Data.messagetype} Message from ${Data.first_name}`,
      text: "",
      react: ContactConfirmationEmailOwner(Data),
    });

    const client = await resend.emails.send({
      from: "Blackcherry | Blackcherry <<EMAIL>>",
      to: [Data.email],
      // bcc: ['iamjulius<PERSON><EMAIL>'],
      subject: `${Data.messagetype} Confirmation`,
      text: "",
      react: ContactConfirmationEmail({
        first_name: Data.first_name,
        messagetype: Data.messagetype,
      }),
    });
    console.log("end");

    return NextResponse.json({ owner, client });
  } catch (error) {
    console.log(error);
    return NextResponse.json({ error });
  }
}
