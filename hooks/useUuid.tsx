import { useState, useEffect } from "react";

function generateUuid() {
  return crypto.randomUUID().split("-").join("").slice(0, 8);
}

export default function useUuid() {
  const [uuid, setUuid] = useState<string | null>(null);

  useEffect(() => {
    const initialUuid = generateUuid();
    setUuid(initialUuid);
  }, []);

  useEffect(() => {
    const interval = setInterval(
      () => {
        const newUuid = generateUuid();
        setUuid(newUuid);
      },
      1000 * 60 * 20,
    );

    return () => clearInterval(interval);
  }, []);

  return uuid;
}
