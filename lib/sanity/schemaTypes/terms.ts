//@ts-nocheck
import { defineField, defineType } from 'sanity'
import { FileText } from 'lucide-react'

export default defineType({
 name: "terms",
 title: "Terms And Conditions",
 icon: FileText,
 type: 'document',
 fields: [
  defineField({
   name: 'orderid',
   title: 'Order ID',
   type: 'number',
  }),
  defineField({
   name: "title",
   title: "Title",
   type: 'string',
  }),
  defineField({
   name: 'slug',
   title: 'Slug',
   description: 'Always genereate to get the slug from the title',
   type: 'slug',
   options: {
    source: 'title',
    maxLength: 112,
   },
   validation: (Rule) => Rule.required(),
  }),
  defineField({
   name: "description",
   title: "Description",
   description: "The description For The About Page",
   type: "array",
   of: [{ type: "block" }],
  }),
 ],
 preview: {
  select: {
   title: 'title',
   subtitlee: 'subtitle',
   media: "image"
  },
  prepare(selection) {
   const { subtitlee } = selection
   return { ...selection, subtitle: `This is the terms and conditions` }
  },
 },
})
