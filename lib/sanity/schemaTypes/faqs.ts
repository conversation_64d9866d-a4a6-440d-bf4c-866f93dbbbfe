import { FileQuestionIcon } from 'lucide-react'
import { defineType } from 'sanity'

export default defineType({
 name: 'faqs',
 title: 'Frequently Asked Questions',
 icon: FileQuestionIcon,
 type: 'document',
 fields: [
  {
   name: 'orderid',
   title: 'orderID',
   type: 'number',
   validation: (Rule) => Rule.required(),
  },
  {
   name: 'question',
   title: 'Question',
   description: "The Question",
   type: 'string',
   validation: (Rule) => Rule.required(),
  },
  {
   name: 'answer',
   title: 'Answer',
   description: "The Answer",
   type: 'text',
   validation: (Rule) => Rule.required(),
  },
 ],
 preview: {
  select: {
   title: 'question',
   orderid: 'orderid',
  },
  prepare(selection) {
   const { orderid } = selection
   return { ...selection, subtitle: orderid && `order ${orderid}` }
  },
 },
})
