//@ts-nocheck
import { defineField, defineType } from "sanity";
import { ShoppingBasket } from "lucide-react";

export default defineType({
  name: "iadoreproducts",
  title: "i-adore Products",
  icon: ShoppingBasket,
  type: "document",
  fields: [
    defineField({
      name: "orderid",
      title: "Order ID",
      type: "number",
    }),
    defineField({
      name: "packageName",
      title: "Package Name",
      type: "string",
    }),
    defineField({
      name: "slug",
      title: "Slug",
      description: "Always genereate to get the slug from the package name",
      type: "slug",
      options: {
        source: "packageName",
        maxLength: 112,
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "price",
      title: "Price",
      description: "The Price For The Package",
      type: "number",
    }),
    defineField({
      name: "currency",
      title: "Currency",
      description: "The currency of the Price For The Package",
      type: "string",
      // validation: Rule => Rule.required().custom(value => ['gh', 'usa'].includes(value)),
      options: {
        list: [
          { title: "Ghana", value: "gh" },
          { title: "United States", value: "usa" },
        ],
        layout: "radio", // This will render radio buttons for the options
      },
      initialValue: "gh",
    }),
    defineField({
      name: "stock",
      title: "Stock",
      description: "The Stock Of The Package",
      type: "string",
      validation: (Rule) =>
        Rule.required().custom((value) =>
          ["in-stock", "out-of-stock"].includes(value)
        ),
      options: {
        list: [
          { title: "In Stock", value: "in-stock" },
          { title: "Out Of Stock", value: "out-of-stock" },
        ],
        layout: "radio", // This will render radio buttons for the options
      },
      initialValue: "in-stock",
    }),
    defineField({
      name: "coverImage",
      title: "Cover Image",
      description: "The Image Cover For The Ratecard ",
      type: "image",
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: "alt",
          type: "string",
          title: "Alternative text",
          initialValue: "img",
          validation: (Rule) => Rule.required(),
        },
      ],
    }),
    {
      name: "features",
      title: "Features",
      description: "The Features For The Ratecard",
      type: "array",
      of: [{ type: "block" }],
    },
  ],
  preview: {
    select: {
      title: "packageName",
      orderid: "orderid",
      price: "price",
      media: "coverImage",
      stock: "stock",
    },
    prepare(selection) {
      const { orderid, price, stock } = selection;
      return {
        ...selection,
        subtitle: orderid && `${stock} | id:${orderid} | GHS${price}`,
      };
    },
  },
});
