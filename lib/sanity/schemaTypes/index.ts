// Import schema types from existing Sanity implementation
import about from "./about";
import bc360content from "./bc-360-content";
import blockContent from "./blockContent";
import faqs from "./faqs";
import gallery from "./gallery";
import iadoreProducts from "./i-adore-products";
import imagecomponents from "./imagecomponents";
import ratecard from "./ratecard";
import seynaturellecontent from "./sey-naturelle";
import seyNaturelleProducts from "./sey-naturelle-products";
import storeProducts from "./store-products";
import terms from "./terms";

export const schemaTypes = [
  // Existing schema types from original Sanity implementation
  imagecomponents,
  ratecard,
  gallery,
  blockContent,
  storeProducts,
  seyNaturelleProducts,
  iadoreProducts,
  about,
  bc360content,
  seynaturellecontent,
  terms,
  faqs,
];

// Export individual schema types for use in sanity.config.ts
export {
  about,
  bc360content,
  blockContent,
  faqs,
  gallery,
  iadoreProducts,
  imagecomponents,
  ratecard,
  seynaturellecontent,
  seyNaturelleProducts,
  storeProducts,
  terms,
};

export default schemaTypes;
