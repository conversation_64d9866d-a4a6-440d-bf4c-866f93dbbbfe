//@ts-nocheck
import { defineField, defineType } from 'sanity'
import { SquareUser } from 'lucide-react'

export default defineType({
 name: "about",
 title: "About",
 icon: SquareUser,
 type: 'document',
 fields: [
  defineField({
   name: 'orderid',
   title: 'Order ID',
   type: 'number',
  }),
  defineField({
   name: "title",
   title: "Title",
   type: 'string',
  }),
  defineField({
   name: 'slug',
   title: 'Slug',
   description: 'Always genereate to get the slug from the title',
   type: 'slug',
   options: {
    source: 'title',
    maxLength: 112,
   },
   validation: (Rule) => Rule.required(),
  }),
  defineField({
   name: "subtitle",
   title: "Sub Title",
   type: 'string',
  }),
  defineField({
   name: "instagram",
   title: "Instagram Link",
   description: "The link for instagram",
   type: "string",
  }),
  defineField({
   name: "whatsapp",
   title: "WhatsApp Link",
   description: "The link for whatsapp",
   type: "string",
  }),
  define<PERSON><PERSON>({
   name: "image",
   title: "Image",
   description: "The About Image",
   type: "image",
   options: {
    hotspot: true,
   },
   fields: [
    {
     name: 'alt',
     type: 'string',
     title: 'Alternative text',
     initialValue: 'img',
     validation: (Rule) => Rule.required(),
    },
   ],
  }),
  defineField({
   name: "description",
   title: "Description",
   description: "The description For The About Page",
   type: "array",
   of: [{ type: "block" }],
  }),
  // IMAGE ONE
  defineField({
   name: "image1",
   title: "Image 1",
   description: "The About Image",
   type: "image",
   options: {
    hotspot: true,
   },
   fields: [
    {
     name: 'alt',
     type: 'string',
     title: 'Alternative text',
     initialValue: 'img',
     validation: (Rule) => Rule.required(),
    },
   ],
  }),
  // IMAGE TWO
  defineField({
   name: "image2",
   title: "Image 2",
   description: "The About Image",
   type: "image",
   options: {
    hotspot: true,
   },
   fields: [
    {
     name: 'alt',
     type: 'string',
     title: 'Alternative text',
     initialValue: 'img',
     validation: (Rule) => Rule.required(),
    },
   ],
  }),
  // IMAGE THREE
  defineField({
   name: "image3",
   title: "Image 3",
   description: "The About Image",
   type: "image",
   options: {
    hotspot: true,
   },
   fields: [
    {
     name: 'alt',
     type: 'string',
     title: 'Alternative text',
     initialValue: 'img',
     validation: (Rule) => Rule.required(),
    },
   ],
  }),
  // IMAGE FOUR
  defineField({
   name: "image4",
   title: "Image 4",
   description: "The About Image",
   type: "image",
   options: {
    hotspot: true,
   },
   fields: [
    {
     name: 'alt',
     type: 'string',
     title: 'Alternative text',
     initialValue: 'img',
     validation: (Rule) => Rule.required(),
    },
   ],
  }),
  // IMAGE FIVE
  defineField({
   name: "image5",
   title: "Image 5",
   description: "The About Image",
   type: "image",
   options: {
    hotspot: true,
   },
   fields: [
    {
     name: 'alt',
     type: 'string',
     title: 'Alternative text',
     initialValue: 'img',
     validation: (Rule) => Rule.required(),
    },
   ],
  }),
  // MESSAGE ONE
  defineField({
   name: "description1",
   title: "Description 1",
   description: "The description For The About Page",
   type: "array",
   of: [{ type: "block" }],
  }),
  // MESSAGE TWO
  defineField({
   name: "description2",
   title: "Description 2",
   description: "The description For The About Page",
   type: "array",
   of: [{ type: "block" }],
  }),
  // MESSAGE THREE 
  defineField({
   name: "description3",
   title: "Description 3",
   description: "The description For The About Page",
   type: "array",
   of: [{ type: "block" }],
  }),
 ],
 preview: {
  select: {
   title: 'title',
   subtitle: 'subtitle',
   media: "image"
  },
  prepare(selection) {
   return { ...selection, }
  },
 },
})
