import { BookImage } from 'lucide-react'
import { defineType } from 'sanity'

export default defineType({
  name: 'gallery',
  title: 'Gallery Images',
  type: 'document',
  icon: BookImage,
  fields: [
    {
      name: 'orderid',
      title: 'orderID',
      type: 'number',
    },
    {
      name: 'imageName',
      title: 'Image Name',
      type: 'string',
    },
    {
      name: 'slug',
      title: 'Slug',
      description: 'This is for querry eg.wedding001',
      type: 'slug',
      options: {
        source: 'imageName',
        maxLength: 112,
      },
    },
    {
      name: 'images',
      type: 'array',
      title: 'Images section one',
      of: [
        {
          name: 'image',
          type: 'image',
          title: 'Image',
          options: {
            hotspot: true,
          },
          fields: [
            {
              name: 'alt',
              type: 'string',
              title: 'Alternative text',
              initialValue: "img",
              validation: (Rule) => Rule.required(),
            },
          ],
        },
      ],
      options: {
        layout: 'grid',
      },
    },
    {
      name: 'secondimages',
      type: 'array',
      title: 'Images section two',
      of: [
        {
          name: 'image',
          type: 'image',
          title: 'Image',
          options: {
            hotspot: true,
          },
          fields: [
            {
              name: 'alt',
              type: 'string',
              title: 'Alternative text',
              initialValue: "img",
              validation: (Rule) => Rule.required(),
            },
          ],
        },
      ],
      options: {
        layout: 'grid',
      },
    },
  ],
  preview: {
    select: {
      title: 'imageName',
      orderid: 'order',
      media: 'images.0',
    },
    prepare(selection) {
      const { orderid } = selection
      return { ...selection, subtitle: orderid && `by ${orderid}` }
    },
  },
})
