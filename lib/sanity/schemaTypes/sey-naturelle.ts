//@ts-nocheck
import { defineField, defineType } from "sanity";
import { Sprout } from "lucide-react";

export default defineType({
  name: "seynaturellecontent",
  title: "Sey Naturelle",
  icon: Sprout,
  type: "document",
  fields: [
    defineField({
      name: "orderid",
      title: "Order ID",
      type: "number",
    }),
    defineField({
      name: "title",
      title: "Title",
      type: "string",
    }),
    defineField({
      name: "slug",
      title: "Slug",
      description: "Always genereate to get the slug from the title",
      type: "slug",
      options: {
        source: "title",
        maxLength: 112,
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "subtitle",
      title: "Sub Title",
      type: "text",
    }),
    // SECTION ONE
    defineField({
      name: "imageone",
      title: "First Image",
      description: "The First Image",
      type: "image",
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: "alt",
          type: "string",
          title: "Alternative text",
          initialValue: "img",
          validation: (Rule) => Rule.required(),
        },
      ],
    }),
    define<PERSON>ield({
      name: "descriptionone",
      title: "First Description",
      description: "The description For The first Image",
      type: "array",
      of: [{ type: "block" }],
    }),
    // SECTION TWO
    defineField({
      name: "imagetwo",
      title: "2nd Image",
      description: "The 2nd Image",
      type: "image",
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: "alt",
          type: "string",
          title: "Alternative text",
          initialValue: "img",
          validation: (Rule) => Rule.required(),
        },
      ],
    }),
    defineField({
      name: "descriptiontwo",
      title: "2nd Description",
      description: "The description For The 2nd Image",
      type: "array",
      of: [{ type: "block" }],
    }),
  ],
  preview: {
    select: {
      title: "title",
      subtitle: "subtitle",
      media: "imageone",
    },
    prepare(selection) {
      return { ...selection };
    },
  },
});
