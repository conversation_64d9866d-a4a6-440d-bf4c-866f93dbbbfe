import groq from "groq";

// Existing schema type fields from original implementation
const storeProductsFields = groq`
  _id,
  orderid,
  packageName,
  "slug": slug.current,
  price,
  compareAtPrice,
  stock,
  coverImage{
    ...,
    "dimensions": asset->metadata.dimensions
  },
  description,
  features
`;

const seyNaturelleProductsFields = groq`
  _id,
  orderid,
  packageName,
  "slug": slug.current,
  price,
  compareAtPrice,
  stock,
  coverImage{
    ...,
    "dimensions": asset->metadata.dimensions
  },
  description,
  features
`;

const imagecomponentsFields = groq`
  orderid,
  imageName,
  "slug": slug.current,
  images[]{
    ...,
    _type == 'image' => {
      ...,
      "dimensions": asset->metadata.dimensions
    },
  }
`;

const faqsFields = groq`
  _id,
  orderid,
  question,
  answer
`;

const termsFields = groq`
  _id,
  orderid,
  title,
  "slug": slug.current,
  description
`;

const aboutFields = groq`
  _id,
  orderid,
  title,
  "slug": slug.current,
  subtitle,
  instagram,
  whatsapp,
  image{
    ...,
    "dimensions": asset->metadata.dimensions
  },
  description,
  description2,
  description3
`;

const bc360Fields = groq`
  _id,
  orderid,
  title,
  "slug": slug.current,
  subtitle,
  imageone{
    ...,
    "dimensions": asset->metadata.dimensions
  },
  descriptionone,
  imagetwo{
    ...,
    "dimensions": asset->metadata.dimensions
  },
  descriptiontwo,
  imagethree{
    ...,
    "dimensions": asset->metadata.dimensions
  },
  descriptionthree,
  packages
`;

const seynaturellecontentFields = groq`
  _id,
  orderid,
  title,
  "slug": slug.current,
  subtitle,
  imageone{
    ...,
    "dimensions": asset->metadata.dimensions
  },
  descriptionone,
  imagetwo{
    ...,
    "dimensions": asset->metadata.dimensions
  },
  descriptiontwo
`;

const rateCardFields = groq`
  _id,
  orderid,
  packageName,
  "slug": slug.current,
  ratetype,
  price,
  coverImage{
    ...,
    "dimensions": asset->metadata.dimensions
  },
  features
`;

// New schema type fields
// Common fields for products
const productFields = groq`
  _id,
  _createdAt,
  name,
  "slug": slug.current,
  description,
  price,
  "categories": categories[]->{ _id, name, "slug": slug.current },
  images[]{
    ...,
    _type == 'image' => {
      ...,
      "dimensions": asset->metadata.dimensions
    }
  },
  sizes[]->{ _id, name, value },
  inStock,
  featured
`;

// Common fields for categories
const categoryFields = groq`
  _id,
  _createdAt,
  name,
  "slug": slug.current,
  description,
  "image": image{
    ...,
    "dimensions": asset->metadata.dimensions
  }
`;

// Common fields for banners
const bannerFields = groq`
  _id,
  _createdAt,
  name,
  "slug": slug.current,
  headline,
  subheading,
  ctaText,
  ctaLink,
  "image": image{
    ...,
    "dimensions": asset->metadata.dimensions
  }
`;

// Common fields for FAQs
const faqFields = groq`
  _id,
  _createdAt,
  question,
  answer,
  orderRank
`;

// Common fields for terms (new schema)
const newTermsFields = groq`
  _id,
  _createdAt,
  title,
  content
`;

// Common fields for privacy policy
const privacyFields = groq`
  _id,
  _createdAt,
  title,
  content
`;

// Common fields for general settings
const generalFields = groq`
  _id,
  _createdAt,
  siteName,
  siteDescription,
  contactEmail,
  contactPhone,
  socialLinks
`;

// Product queries
export const allProductsQuery = groq`
  *[_type == "product"] | order(featured desc, _createdAt desc) {
    ${productFields}
  }
`;

export const featuredProductsQuery = groq`
  *[_type == "product" && featured == true] | order(_createdAt desc) {
    ${productFields}
  }
`;

export const productBySlugQuery = (slug: string) => groq`
  *[_type == "product" && slug.current == "${slug}"][0] {
    ${productFields}
  }
`;

export const productsByCategoryQuery = (categorySlug: string) => groq`
  *[_type == "product" && references(*[_type == "category" && slug.current == "${categorySlug}"]._id)] {
    ${productFields}
  }
`;

// Category queries
export const allCategoriesQuery = groq`
  *[_type == "category"] | order(name asc) {
    ${categoryFields}
  }
`;

export const categoryBySlugQuery = (slug: string) => groq`
  *[_type == "category" && slug.current == "${slug}"][0] {
    ${categoryFields}
  }
`;

// Banner queries
export const allBannersQuery = groq`
  *[_type == "banner"] | order(_createdAt desc) {
    ${bannerFields}
  }
`;

export const activeBannersQuery = groq`
  *[_type == "banner" && active == true] | order(_createdAt desc) {
    ${bannerFields}
  }
`;

// FAQ queries
export const allFaqsQuery = groq`
  *[_type == "faqs"] | order(orderRank) {
    ${faqFields}
  }
`;

// Terms query
export const termsQuery = groq`
  *[_type == "terms"][0] {
    ${termsFields}
  }
`;

// Privacy policy query
export const privacyQuery = groq`
  *[_type == "privacy"][0] {
    ${privacyFields}
  }
`;

// General settings query
export const generalSettingsQuery = groq`
  *[_type == "general"][0] {
    ${generalFields}
  }
`;

// Disclosure queries
export const allDisclosuresQuery = groq`
  *[_type == "disclosure"] | order(_createdAt desc) {
    _id,
    _createdAt,
    title,
    "slug": slug.current,
    content
  }
`;

export const disclosureBySlugQuery = (slug: string) => groq`
  *[_type == "disclosure" && slug.current == "${slug}"][0] {
    _id,
    _createdAt,
    title,
    "slug": slug.current,
    content
  }
`;

// Existing schema type queries
// Store Products queries
export const storeProductsQuery = groq`
  *[_type == "storeproducts"] | order(orderid) {
    ${storeProductsFields}
  }
`;

export const storeProductQuery = (slug: string) => groq`
  *[_type == "storeproducts" && slug.current == "${slug}"][0] {
    ${storeProductsFields}
  }
`;

// Sey Naturelle Products queries
export const seyNaturelleProductsQuery = groq`
  *[_type == "seynaturelleproducts"] | order(orderid) {
    ${seyNaturelleProductsFields}
  }
`;

export const seyNaturelleProductQuery = (slug: string) => groq`
  *[_type == "seynaturelleproducts" && slug.current == "${slug}"][0] {
    ${seyNaturelleProductsFields}
  }
`;

// i-adore Products queries
export const iadoreProductsQuery = groq`
  *[_type == "iadoreproducts"] | order(orderid) {
    ${storeProductsFields}
  }
`;

export const iadoreProductQuery = (slug: string) => groq`
  *[_type == "iadoreproducts" && slug.current == "${slug}"][0] {
    ${storeProductsFields}
  }
`;

// Image Components queries
export const imageComponentsQuery = groq`
  *[_type == "imagecomponents"] | order(orderid) {
    ${imagecomponentsFields}
  }
`;

export const imageComponentQuery = (slug: string) => groq`
  *[_type == "imagecomponents" && slug.current == "${slug}"][0] {
    ${imagecomponentsFields}
  }
`;

// FAQs query
export const faqsQuery = groq`
  *[_type == "faqs"] | order(orderid) {
    ${faqsFields}
  }
`;

// Terms query (existing schema)
export const existingTermsQuery = groq`
  *[_type == "terms"] | order(orderid)[0] {
    ${termsFields}
  }
`;

// About query
export const aboutQuery = groq`
  *[_type == "about"] | order(orderid)[0] {
    ${aboutFields}
  }
`;

// BC 360 query
export const bc360Query = groq`
  *[_type == "bc360content"] | order(orderid)[0] {
    ${bc360Fields}
  }
`;

// Sey Naturelle Content query
export const seyNaturelleContentQuery = groq`
  *[_type == "seynaturellecontent"] | order(orderid)[0] {
    ${seynaturellecontentFields}
  }
`;

// Rate Card query
export const rateCardQuery = groq`
  *[_type == "ratecard"] | order(orderid) {
    ${rateCardFields}
  }
`;
