import { client } from "../client";
import {
  about<PERSON><PERSON>y,
  bc360<PERSON><PERSON>y,
  existingTermsQuery,
  faqsQuery,
  iadoreProductQuery,
  iadoreProductsQuery,
  imageComponentQuery,
  imageComponentsQuery,
  rateCardQuery,
  seyNaturelleContentQuery,
  seyNaturelleProductQuery,
  seyNaturelleProductsQuery,
  storeProductQuery,
  storeProductsQuery,
} from "./querry";

// Store Products actions
export async function getAllStoreProducts() {
  try {
    const data = await client.fetch(storeProductsQuery);
    return data;
  } catch (error) {
    console.error("Error fetching all store products:", error);
    throw new Error("Failed to fetch store products");
  }
}

export async function getStoreProductBySlug(slug: string) {
  try {
    const data = await client.fetch(storeProductQuery(slug));
    return data;
  } catch (error) {
    console.error(`Error fetching store product with slug ${slug}:`, error);
    throw new Error(`Failed to fetch store product with slug ${slug}`);
  }
}

// Sey Naturelle Products actions
export async function getAllSeyNaturelleProducts() {
  try {
    const data = await client.fetch(seyNaturelleProductsQuery);
    return data;
  } catch (error) {
    console.error("Error fetching all Sey Naturelle products:", error);
    throw new Error("Failed to fetch Sey Naturelle products");
  }
}

export async function getSeyNaturelleProductBySlug(slug: string) {
  try {
    const data = await client.fetch(seyNaturelleProductQuery(slug));
    return data;
  } catch (error) {
    console.error(
      `Error fetching Sey Naturelle product with slug ${slug}:`,
      error,
    );
    throw new Error(`Failed to fetch Sey Naturelle product with slug ${slug}`);
  }
}

// i-adore Products actions
export async function getAllIAdoreProducts() {
  try {
    const data = await client.fetch(iadoreProductsQuery);
    return data;
  } catch (error) {
    console.error("Error fetching all i-adore products:", error);
    throw new Error("Failed to fetch i-adore products");
  }
}

export async function getIAdoreProductBySlug(slug: string) {
  try {
    const data = await client.fetch(iadoreProductQuery(slug));
    return data;
  } catch (error) {
    console.error(`Error fetching i-adore product with slug ${slug}:`, error);
    throw new Error(`Failed to fetch i-adore product with slug ${slug}`);
  }
}

// Image Components actions
export async function getAllImageComponents() {
  try {
    const data = await client.fetch(imageComponentsQuery);
    return data;
  } catch (error) {
    console.error("Error fetching all image components:", error);
    throw new Error("Failed to fetch image components");
  }
}

export async function getImageComponentBySlug(slug: string) {
  try {
    const data = await client.fetch(imageComponentQuery(slug));
    return data;
  } catch (error) {
    console.error(`Error fetching image component with slug ${slug}:`, error);
    throw new Error(`Failed to fetch image component with slug ${slug}`);
  }
}

// FAQs action
export async function getFaqs() {
  try {
    const data = await client.fetch(faqsQuery);
    return data;
  } catch (error) {
    console.error("Error fetching FAQs:", error);
    throw new Error("Failed to fetch FAQs");
  }
}

// Terms action
export async function getTerms() {
  try {
    const data = await client.fetch(existingTermsQuery);
    return data;
  } catch (error) {
    console.error("Error fetching terms:", error);
    throw new Error("Failed to fetch terms");
  }
}

// About action
export async function getAbout() {
  try {
    const data = await client.fetch(aboutQuery);
    return data;
  } catch (error) {
    console.error("Error fetching about:", error);
    throw new Error("Failed to fetch about");
  }
}

// BC 360 action
export async function getBC360() {
  try {
    const data = await client.fetch(bc360Query);
    return data;
  } catch (error) {
    console.error("Error fetching BC 360:", error);
    throw new Error("Failed to fetch BC 360");
  }
}

// Sey Naturelle Content action
export async function getSeyNaturelleContent() {
  try {
    const data = await client.fetch(seyNaturelleContentQuery);
    return data;
  } catch (error) {
    console.error("Error fetching Sey Naturelle content:", error);
    throw new Error("Failed to fetch Sey Naturelle content");
  }
}

// Rate Card action
export async function getRateCards() {
  try {
    const data = await client.fetch(rateCardQuery);
    return data;
  } catch (error) {
    console.error("Error fetching rate cards:", error);
    throw new Error("Failed to fetch rate cards");
  }
}

// Gallery functions
export async function getGallerySlugs() {
  const query = `*[_type == "gallery"] | order(orderid) .slug.current`;
  return await client.fetch(query);
}

export async function getGalleryBySlug(slug: string) {
  const query = `*[_type == "gallery" && slug.current == "${slug}"]{
    _id,
    orderid,
    imageName,
    "slug": slug.current,
    images[]{
      ...,
      _type == 'image' => {
        ...,
        "dimensions": asset->metadata.dimensions
      },
    },
    secondimages[]{
      ...,
      _type == 'image' => {
        ...,
        "dimensions": asset->metadata.dimensions
      },
    },
  }`;
  return await client.fetch(query);
}

// Gallery categories function
export async function getGalleryCategories() {
  const query = `*[_type == "gallery"] | order(orderid) {
    _id,
    orderid,
    imageName,
    "slug": slug.current
  }`;
  return await client.fetch(query);
}
