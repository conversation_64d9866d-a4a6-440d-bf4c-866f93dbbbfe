import { supabaseServer } from "./server";

export async function getSession() {
  const supabase = await supabaseServer();
  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    return session;
  } catch (error) {
    console.error("Error:", error);
    return null;
  }
}

// Secure function to get authenticated user
// This function first checks for a session, then verifies the user with the server
export async function getAuthenticatedUser() {
  const supabase = await supabaseServer();
  try {
    // First check if there's a session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session?.user) {
      return null;
    }

    // If session exists, verify the user with the server to ensure authenticity
    // This is the secure approach recommended by Supabase
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError) {
      // If getUser fails but we have a session, the session might be invalid
      console.error("Error verifying user authenticity:", userError);
      return null;
    }

    return user;
  } catch (error) {
    console.error("Error in getAuthenticatedUser:", error);
    return null;
  }
}

// Alternative secure function that handles edge cases better
export async function getVerifiedUser() {
  const supabase = await supabaseServer();

  try {
    // Get session first to avoid AuthSessionMissingError
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    // If no session or session error, user is not authenticated
    if (sessionError || !session) {
      return null;
    }

    // Session exists, now verify with server for security
    try {
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();

      if (userError || !user) {
        // Session exists but user verification failed - session might be stale
        return null;
      }

      return user;
    } catch (userVerificationError) {
      // getUser failed, but we have a session - handle gracefully
      console.warn(
        "User verification failed, but session exists:",
        userVerificationError,
      );
      return null;
    }
  } catch (error) {
    console.error("Error in getVerifiedUser:", error);
    return null;
  }
}
