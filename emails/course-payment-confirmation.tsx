import { currency } from "@/hooks/use-currency";
import { coursePaymentDataType } from "@/types/types";
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Text,
} from "@react-email/components";
import * as React from "react";

type Data = coursePaymentDataType;
const websiteUrl = "https://www.blackcherrygh.com";
const logoUrl = "https://www.blackcherrygh.com/images/logo.png";
export const RegistrationConfirmationEmailOwner = (data: Data) => (
  <Html>
    <Head />
    <Preview>New Purchase For Crash Course</Preview>
    <Body style={main}>
      <Container style={container}>
        <Img
          src={logoUrl}
          style={{
            marginTop: "16px",
          }}
          width="82"
          height="65"
          alt="Logo"
        />
        <Heading style={h1}>New Purchase For Crash Course</Heading>
        <Text
          style={{
            ...text,
            marginBottom: "14px",
            fontWeight: "600",
            color: "gray",
          }}
        >
          <span style={{ color: "black", fontWeight: "700" }}>
            Here Are the details of the Registration
          </span>
          <br />
          <span style={{ color: "black", fontWeight: "700" }}>Full Name</span>
          <br /> {data.payee.full_name} <br />
          <span style={{ color: "black", fontWeight: "700" }}>Email</span>
          <br /> {data.payee.email} <br />
          <span style={{ color: "black", fontWeight: "700" }}>
            Phone Number
          </span>
          <br /> {data.payee.phone_number} <br />
          <span style={{ color: "black", fontWeight: "700" }}>
            Whatsapp Phone Number
          </span>
          <br /> {data.payee.whatsapp_number} <br />
          <span style={{ color: "black", fontWeight: "700" }}>Age</span>
          <br /> {data.payee.age} <br />
          <span style={{ color: "black", fontWeight: "700" }}>Occupation</span>
          <br /> {data.payee.occupation} <br />
          <span style={{ color: "black", fontWeight: "700" }}>
            Most Interested Topics
          </span>
          <br />{" "}
          {data.payee.most_interested.map((d, i) => (
            <span key={i}>{d},</span>
          ))}{" "}
          <br />
          <span style={{ color: "black", fontWeight: "700" }}>
            Most difficult aspect
          </span>
          <br />{" "}
          {data.payee.difficult_aspect.map((d, i) => (
            <span key={i}>{d},</span>
          ))}{" "}
          <br />
          <span style={{ color: "black", fontWeight: "700" }}>
            Social Referal
          </span>
          <br /> {data.payee.social_referal} <br />
        </Text>
        <Text style={footer}>
          <Link
            href={websiteUrl}
            target="_blank"
            style={{ ...link, color: "#898989" }}
          >
            Black Cherry
          </Link>
          <br />
          COSMETIC CONSULTANCY, BEAUTY SERVICES, <br /> TRAINING SERVICES ON
          COSMETIC
        </Text>
      </Container>
    </Body>
  </Html>
);
export const RegistrationConfirmationEmail = (data: Data) => (
  <Html>
    <Head />
    <Preview>Registration Confirmation for Crash Course Training</Preview>
    <Body style={main}>
      <Container style={container}>
        <Img
          src={logoUrl}
          style={{
            marginTop: "16px",
          }}
          width="82"
          height="65"
          alt="Logo"
        />
        <Heading style={h1}>Registration Confirmation</Heading>
        <Text
          style={{
            ...text,
            marginBottom: "14px",
            fontWeight: "600",
            color: "black",
          }}
        >
          <span>Hello {data.payee.full_name},</span>
          <br />
          <span>Your have been registered successfully.</span>
        </Text>
        <Text
          style={{
            ...text,
            marginBottom: "14px",
            fontWeight: "600",
            color: "gray",
          }}
        >
          <span style={{ color: "black", fontWeight: "700" }}>
            Here Are Your Details
          </span>
          <br />
          <span style={{ color: "black", fontWeight: "700" }}>Amount Paid</span>
          <br /> {currency(650, "GHS")} <br />
          <span style={{ color: "black", fontWeight: "700" }}>Reference</span>
          <br /> {data.reference.reference} <br />
          <span style={{ color: "black", fontWeight: "700" }}>
            Transaction No.
          </span>
          <br /> {data.reference.transaction} <br />
          <span style={{ color: "black", fontWeight: "700" }}>
            Purchase Date
          </span>
          <br />- {data.date} <br />
        </Text>
        <Text style={footer}>
          <Link
            href="https://www.blackcherrygh.com/"
            target="_blank"
            style={{ ...link, color: "black", fontWeight: "600" }}
          >
            Black Cherry
          </Link>
          <br />
          COSMETIC CONSULTANCY, BEAUTY SERVICES, <br /> TRAINING SERVICES ON
          COSMETIC
          <br />
          Please contact us if you have any questions. <br /> (You can send your
          questions to our{" "}
          <Link
            href="https://www.blackcherrygh.com/contact"
            target="_blank"
            style={{ ...link, color: "#898989" }}
          >
            support page
          </Link>{" "}
          .)
        </Text>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: "#ffffff",
};

const container = {
  paddingLeft: "12px",
  paddingRight: "12px",
  margin: "0 auto",
};

const h1 = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "24px",
  fontWeight: "bold",
  margin: "0",
  padding: "0",
};

const link = {
  color: "#2754C5",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  textDecoration: "underline",
};

const text = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  margin: "24px 0",
};

const footer = {
  color: "#898989",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "12px",
  lineHeight: "22px",
  marginTop: "12px",
  marginBottom: "24px",
};

const code = {
  display: "inline-block",
  padding: "16px 4.5%",
  width: "90.5%",
  backgroundColor: "#f4f4f4",
  borderRadius: "5px",
  border: "1px solid #eee",
  color: "#333",
};
