import { contactformSchema } from "@/components/forms/contact/contact-form";
import { cn } from "@/lib/utils";
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Text,
} from "@react-email/components";
import * as React from "react";
import { z } from "zod";

type userData = z.infer<typeof contactformSchema>;
const websiteUrl = "https://www.blackcherrygh.com";
const logoUrl = "https://www.blackcherrygh.com/images/logo.png";

export const ContactConfirmationEmailOwner = (user: userData) => (
  <Html>
    <Head />
    <Preview>New Message From {user.first_name} On Website</Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>Booking Confirmation</Heading>
        <Text style={{ ...text, marginBottom: "14px" }}>
          This is the details of the user that booked the shoot.
        </Text>
        <Text style={{ ...text, marginBottom: "14px" }}>
          Message Type : {user.messagetype} <br />
          Full Name : {user.first_name}
          <br />
          Email : {user.email} <br />
          Phone Number : {user.phone} <br />
          Message : {user.message} <br />
        </Text>

        <Text style={footer}>
          <Link
            href='https://www.blackcherrygh.com/'
            target='_blank'
            style={{ ...link, color: "#898989" }}
          >
            Black Cherry
          </Link>
          , COSMETIC CONSULTANCY, BEAUTY SERVICES, TRAINING SERVICES ON COSMETIC
        </Text>
      </Container>
    </Body>
  </Html>
);
export const ContactConfirmationEmail = ({
  first_name,
  messagetype,
}: {
  first_name: userData["first_name"];
  messagetype: userData["messagetype"];
}) => (
  <Html>
    <Head />
    <Preview>{messagetype} Message Sent To Blackcherry</Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>Message To Blackcherry</Heading>
        <Text style={{ ...text, marginBottom: "14px" }}>
          Dear, {first_name}.
        </Text>
        <Text style={{ ...text, marginBottom: "14px" }}>
          Your {messagetype} message has been received and we will get back to
          you soon.
        </Text>
        <Img
          src={logoUrl}
          style={{
            marginTop: "16px",
          }}
          width='82'
          height='65'
          alt='Logo'
        />
        <Text style={footer}>
          <Link
            href='https://www.blackcherrygh.com/'
            target='_blank'
            style={{ ...link, color: "black", fontWeight: "600" }}
          >
            Black Cherry
          </Link>
          <br />
          COSMETIC CONSULTANCY, BEAUTY SERVICES, <br /> TRAINING SERVICES ON
          COSMETIC
          <br />
          Please contact us if you have any questions. <br /> (You can send your
          questions to our{" "}
          <Link
            href='https://www.blackcherrygh.com/contact'
            target='_blank'
            style={{ ...link, color: "#898989" }}
          >
            support page
          </Link>{" "}
          .)
        </Text>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: "#ffffff",
};

const container = {
  paddingLeft: "12px",
  paddingRight: "12px",
  margin: "0 auto",
};

const h1 = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "24px",
  fontWeight: "bold",
  margin: "0",
  padding: "0",
};

const link = {
  color: "#2754C5",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  textDecoration: "underline",
};

const text = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  margin: "24px 0",
};

const footer = {
  color: "#898989",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "12px",
  lineHeight: "22px",
  marginTop: "12px",
  marginBottom: "24px",
};

const code = {
  display: "inline-block",
  padding: "16px 4.5%",
  width: "90.5%",
  backgroundColor: "#f4f4f4",
  borderRadius: "5px",
  border: "1px solid #eee",
  color: "#333",
};
